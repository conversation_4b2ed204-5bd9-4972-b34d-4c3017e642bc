@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Comgás Light Theme - Based on mockups */
    --background: 210 11% 97%; /* #F8F9FA */
    --foreground: 210 10% 15%; /* Dark text */

    --card: 0 0% 98%; /* White cards */
    --card-foreground: 210 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 10% 15%;

    /* Comgás brand colors */
    --primary: 140 86% 45%; /* #1CA633 - Comgás green */
    --primary-foreground: 0 0% 100%;

    --secondary: 140 48% 85%; /* #D4E7D7 - Light green */
    --secondary-foreground: 140 100% 14%; /* #004524 - Dark green */

    --muted: 210 17% 95%; /* #F1F3F4 */
    --muted-foreground: 210 8% 45%; /* #6C757D */

    --accent: 140 48% 85%; /* #D4E7D7 */
    --accent-foreground: 140 100% 14%; /* #004524 */

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 14% 89%; /* #DEE2E6 */
    --input: 210 14% 89%;
    --ring: 140 86% 45%;

    --radius: 0.75rem;

    /* Success colors */
    --success: 140 86% 45%; /* #1CA633 */
    --success-foreground: 0 0% 100%;

    /* Warning colors */
    --warning: 43 96% 56%; /* #FFBF47 */
    --warning-foreground: 0 0% 0%;

    /* Danger colors */
    --danger: 0 84% 67%; /* #FF5D5D */
    --danger-foreground: 0 0% 100%;

    /* Neutral colors for status */
    --neutral: 210 8% 45%; /* #6C757D */
    --neutral-foreground: 0 0% 100%;

    /* Sidebar colors - from mockup */
    --sidebar-background: 0 0% 98%; /* #FBFBFB */
    --sidebar-foreground: 210 10% 15%;
    --sidebar-primary: 140 86% 45%; /* #1CA633 */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 140 48% 85%; /* #D4E7D7 */
    --sidebar-accent-foreground: 140 100% 14%; /* #004524 */
    --sidebar-border: 210 14% 89%; /* #DEE2E6 */
    --sidebar-ring: 140 86% 45%;

    /* Custom gradients and shadows */
    --gradient-primary: linear-gradient(135deg, hsl(140 86% 45%), hsl(140 78% 55%));
    --gradient-success: linear-gradient(135deg, hsl(140 48% 85%), hsl(140 48% 90%));
    --shadow-soft: 0 2px 8px -2px hsl(210 10% 15% / 0.1);
    --shadow-medium: 0 4px 16px -4px hsl(210 10% 15% / 0.15);
    --shadow-strong: 0 8px 32px -8px hsl(210 10% 15% / 0.2);

    /* Animation timing */
    --transition-base: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
