# app/api/routers.py
from __future__ import annotations 
from fastapi import APIRouter, UploadFile, File, status, HTTPException, BackgroundTasks, Request
from pydantic import BaseModel

from mutagen.mp3 import MP3
from mutagen.wave import WAVE
from app.config import ALLOWED_AUDIO_TYPES as allowed_types
from app.config import TRANSCRICOES_DIR, EVAL_WEBHOOK_URL, MODEL_PATH, MONGO_URI, MONGO_DB, MONGO_COLLECTION
import tempfile
import os
import json
import httpx
import whisper
import re
from typing import Dict, Any
import logging
import time
import uuid
from prometheus_client import Counter, Histogram, Gauge
import threading
import sys
import sys, os
# de app/api/routers.py → sobe 3 níveis para chegar em ./src
sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", ".."))
)

# Carrega o Whisper uma vez
WHISPER_MODEL = whisper.load_model("turbo")

router = APIRouter()

# Logger e métricas (Prometheus)
logger = logging.getLogger("app")

TRANSCRIPTIONS_TOTAL = Counter(
    "app_transcriptions_total",
    "Total de transcrições processadas",
    ["status"],
)

TRANSCRIPTION_DURATION_SECONDS = Histogram(
    "app_transcription_duration_seconds",
    "Duração da transcrição (s)",
)

EVALUATIONS_TOTAL = Counter(
    "app_evaluations_total",
    "Total de avaliações NLP",
    ["status"],
)

EVALUATION_DURATION_SECONDS = Histogram(
    "app_evaluation_duration_seconds",
    "Duração da avaliação NLP (s)",
)

WEBHOOK_FAILURES_TOTAL = Counter(
    "app_webhook_failures_total",
    "Falhas ao disparar webhook de avaliação",
)

# Novas métricas estendidas
TRANSCRIPTIONS_IN_FLIGHT = Gauge(
    "app_transcriptions_in_flight",
    "Transcrições em processamento",
)

EVALUATIONS_IN_FLIGHT = Gauge(
    "app_evaluations_in_flight",
    "Avaliações em processamento",
)

TRANSCRIPTIONS_TOTAL_BY_TYPE = Counter(
    "app_transcriptions_total_by_type",
    "Total de transcrições por tipo de arquivo",
    ["status", "type"],
)

TRANSCRIPTION_SEGMENTS = Histogram(
    "app_transcription_segments",
    "Distribuição de quantidade de segmentos por transcrição",
)

WEBHOOK_LATENCY = Histogram(
    "app_webhook_latency_seconds",
    "Latência do POST para o webhook de avaliação (s)",
)

PIPELINE_E2E = Histogram(
    "app_pipeline_end_to_end_seconds",
    "Duração ponta-a-ponta: transcrição -> avaliação (s)",
)

EVALUATION_SCORE = Histogram(
    "app_evaluation_score",
    "Distribuição de notas do modelo (0-10)",
)

# Registro de início de pipeline para cálculo do e2e
_pipeline_start_ts: Dict[str, float] = {}
_pipeline_lock = threading.Lock()

class EvaluateRequest(BaseModel):
    document_name: str

# --------------------------
# Utilidades internas
# --------------------------

def _ensure_dir(path: str) -> None:
    os.makedirs(path, exist_ok=True)

def _proximo_nome_transcricao(dirpath: str) -> str:
    """
    Gera o próximo nome sequencial: transcricao_001.json, transcricao_002.json, ...
    """
    _ensure_dir(dirpath)
    padrao = re.compile(r"^transcricao_(\d{3})\.json$")
    indices = []
    for fn in os.listdir(dirpath):
        m = padrao.match(fn)
        if m:
            indices.append(int(m.group(1)))
    prox = (max(indices) + 1) if indices else 1
    return f"transcricao_{prox:03d}.json"

async def _disparar_webhook_avaliacao(document_name: str, correlation_id: str | None = None) -> None:
    """
    Dispara POST para a API de avaliação com o nome do documento salvo.
    Feito de forma assíncrona (BackgroundTasks).
    """
    headers = {}
    if correlation_id:
        headers["X-Request-Id"] = correlation_id
    try:
        start_ws = time.time()
        async with httpx.AsyncClient(timeout=30) as client:
            # Payload simples: nome do documento
            await client.post(EVAL_WEBHOOK_URL, json={"document_name": document_name}, headers=headers)
        WEBHOOK_LATENCY.observe(time.time() - start_ws)
        logger.info({
                "event": "webhook_enqueued",
                "document_name": document_name,
                "webhook": EVAL_WEBHOOK_URL,
                "correlationId": correlation_id,
        })
    except Exception as exc:
        WEBHOOK_FAILURES_TOTAL.inc()
        logger.error({
            "event": "webhook_error",
            "document_name": document_name,
            "error": str(exc),
            "webhook": EVAL_WEBHOOK_URL,
            "correlationId": correlation_id,
        })

# --------------------------
# Endpoints existentes
# --------------------------

@router.post(
    "/uploadaudio/",
    responses={
        200: {
            "description": "Upload realizado com sucesso",
            "content": {
                "application/json": {
                    "example": {
                        "Arquivo": "audio_exemplo.mp3",
                        "Duração (minutos)": 3.45,
                        "Tipo de arquivo": "audio/mpeg"
                    }
                }
            }
        },
        400: {
            "description": "Erro de validação do arquivo"
        },
        415: {
            "description": "Tipo de mídia não suportado"
        }
    }
)
async def upload_audio(audio_file: UploadFile = File(...)):
    # Valida o tipo do arquivo
    if audio_file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail={
                "code": "Tipo de mídia não suportado",
                "message": f"Apenas arquivos MP3 ou WAV são permitidos. Recebido: {audio_file.content_type}."
            }
        )

    # Para obter duração, precisamos ler o arquivo
    raw_bytes = await audio_file.read()
    if not raw_bytes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": "Arquivo vazio",
                "message": "O arquivo de áudio está vazio."
            }
        )

    suffix = ".mp3" if audio_file.content_type == "audio/mpeg" else ".wav"
    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
        tmp.write(raw_bytes)
        tmp_path = tmp.name

    try:
        if audio_file.content_type == "audio/mpeg":
            audio = MP3(tmp_path)
        else:
            audio = WAVE(tmp_path)
        duration = round(audio.info.length / 60, 2)  # minutos
    finally:
        try:
            os.remove(tmp_path)
        except Exception:
            pass

    return {
        "Arquivo": audio_file.filename,
        "Duração (minutos)": duration,
        "Tipo de arquivo": audio_file.content_type
    }


@router.post(
    "/stt/transcribe",
    responses={
        200: {
            "description": "Transcrição realizada, salva em .json e webhook disparado"
        },
        400: {"description": "Erro de validação do arquivo"},
        415: {"description": "Tipo de mídia não suportado"},
        500: {"description": "Erro interno ao processar o áudio"}
    }
)
async def transcribe_audio(background_tasks: BackgroundTasks, request: Request, audio_file: UploadFile = File(...)):
    start_time = time.time()
    correlation_id = request.headers.get("X-Request-Id", str(uuid.uuid4()))
    TRANSCRIPTIONS_IN_FLIGHT.inc()
    logger.info({
        "event": "transcription_started",
        "filename": getattr(audio_file, "filename", None),
        "contentType": getattr(audio_file, "content_type", None),
        "correlationId": correlation_id,
        "path": "/stt/transcribe",
        "method": "POST",
    })
    # 1) Validação de tipo
    if audio_file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail={
                "code": "Tipo de mídia não suportado",
                "message": f"Apenas arquivos MP3 ou WAV são permitidos. Recebido: {audio_file.content_type}."
            }
        )

    # 2) Conteúdo
    raw_bytes = await audio_file.read()
    if not raw_bytes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": "Arquivo vazio",
                "message": "O arquivo de áudio está vazio."
            }
        )

    # 3) Arquivo temporário
    suffix = ".mp3" if audio_file.content_type == "audio/mpeg" else ".wav"
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
            tmp.write(raw_bytes)
            tmp_path = tmp.name

        # Duração (segundos) se possível
        try:
            if audio_file.content_type == "audio/mpeg":
                audio_meta = MP3(tmp_path)
            else:
                audio_meta = WAVE(tmp_path)
            duration_seconds = float(audio_meta.info.length)
        except Exception:
            duration_seconds = None

        # 4) Transcrever
        result = WHISPER_MODEL.transcribe(tmp_path)

        segments = result.get("segments", []) or []
        if segments:
            confianca_media = sum(seg.get("avg_logprob", 0.0) for seg in segments) / len(segments)
            duracao_total = segments[-1].get("end", 0.0)
        else:
            confianca_media = None
            duracao_total = duration_seconds if duration_seconds is not None else 0.0
        # Observa distribuição de segmentos
        try:
            TRANSCRIPTION_SEGMENTS.observe(float(len(segments)))
        except Exception:
            pass

        texto = (result.get("text") or "").strip()

        payload = {
            "arquivo": audio_file.filename,
            "modelo": "turbo",
            "tipo": audio_file.content_type,
            "duracao_segundos": duracao_total if duracao_total else duration_seconds,
            "confianca_media": confianca_media,
            "segmentos": len(segments),
            "texto": texto
        }

        # 5) Salvar em JSON (transcricao_###.json)
        _ensure_dir(TRANSCRICOES_DIR)
        document_name = _proximo_nome_transcricao(TRANSCRICOES_DIR)
        out_path = os.path.join(TRANSCRICOES_DIR, document_name)
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(payload, f, ensure_ascii=False, indent=2)

        # 6) Disparar webhook (assíncrono)
        background_tasks.add_task(_disparar_webhook_avaliacao, document_name, correlation_id)

        # Registra início do pipeline para cálculo E2E no /nlp/evaluate
        with _pipeline_lock:
            _pipeline_start_ts[document_name] = start_time

        # Métricas e logs de sucesso
        duration_s = time.time() - start_time
        TRANSCRIPTION_DURATION_SECONDS.observe(duration_s)
        TRANSCRIPTIONS_TOTAL.labels(status="success").inc()
        type_label = "mp3" if audio_file.content_type == "audio/mpeg" else "wav"
        TRANSCRIPTIONS_TOTAL_BY_TYPE.labels(status="success", type=type_label).inc()
        logger.info({
            "event": "transcription_saved",
            "document_name": document_name,
            "saved_at": out_path,
            "durationMs": int(duration_s * 1000),
            "segments": len(segments),
            "confianca_media": confianca_media,
            "correlationId": correlation_id,
        })

        return {
            "status": "ok",
            "document_name": document_name,
            "saved_at": out_path,
            "webhook": EVAL_WEBHOOK_URL
        }

    except HTTPException:
        TRANSCRIPTIONS_TOTAL.labels(status="error").inc()
        raise
    except Exception as e:
        TRANSCRIPTIONS_TOTAL.labels(status="error").inc()
        try:
            type_label = "mp3" if getattr(audio_file, "content_type", "") == "audio/mpeg" else "wav"
            TRANSCRIPTIONS_TOTAL_BY_TYPE.labels(status="error", type=type_label).inc()
        except Exception:
            pass
        logger.error({
            "event": "transcription_error",
            "error": str(e),
            "correlationId": correlation_id,
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"code": "Erro interno", "message": f"Falha ao transcrever: {str(e)}"}
        )
    finally:
        try:
            TRANSCRIPTIONS_IN_FLIGHT.dec()
        except Exception:
            pass
        try:
            if 'tmp_path' in locals() and os.path.exists(tmp_path):
                os.remove(tmp_path)
        except Exception:
            pass


# --------------------------
# API do Modelo / Avaliação
# --------------------------

# app/api/routers.py (substitua a função _avaliar_texto_modelo)
# app/api/routers.py  — SUBSTITUA a função _avaliar_texto_modelo por esta

def _avaliar_texto_modelo(texto: str, transcricao_dict: dict) -> dict:
    """
    Usa a função real prever_nota_atendimento() do evaluator.py.
    Monta o novo_atendimento no formato esperado pelo seu modelo.
    """
    try:
        from nlp_model.evaluator import prever_nota_atendimento
    except Exception as e:
        return {
            "erro": "import_failed",
            "detalhe": f"Falha ao importar previsores do modelo: {e}"
        }

    # Monte aqui o dict no formato que sua pipeline espera.
    # Se evaluate_transcription usa mais campos além de 'texto',
    # inclua-os derivando de transcricao_dict.
    novo_atendimento = {
        "texto": texto,
        "raw": transcricao_dict,  # opcional, útil se sua função usa outros campos
    }

    try:
        nota = prever_nota_atendimento(
            novo_atendimento=novo_atendimento,
            channel="0800",
            models=None,
            alpha_ml=0.6,
            penalty_must=0.20
        )
    except Exception as e:
        return {
            "erro": "exec_failed",
            "detalhe": f"Falha ao executar prever_nota_atendimento: {e}"
        }

    # Padroniza a saída para dict
    if isinstance(nota, (int, float)):
        return {"nota_atendimento": float(nota)}
    elif isinstance(nota, dict):
        return nota
    else:
        return {"nota_atendimento": None, "observacoes": "Retorno do modelo em formato não reconhecido"}



@router.post("/nlp/evaluate")
async def avaliar_transcricao(payload: EvaluateRequest, request: Request):
    start_time = time.time()
    correlation_id = request.headers.get("X-Request-Id", str(uuid.uuid4()))
    EVALUATIONS_IN_FLIGHT.inc()
    logger.info({
        "event": "evaluation_started",
        "document_name": payload.document_name,
        "correlationId": correlation_id,
        "path": "/nlp/evaluate",
        "method": "POST",
    })
    document_name = payload.document_name
    path = os.path.join(TRANSCRICOES_DIR, document_name)
    if not os.path.isfile(path):
        raise HTTPException(status_code=404, detail={
            "code": "nao_encontrado",
            "message": f"{document_name} não encontrado em {TRANSCRICOES_DIR}"
        })

    # 1) Carrega o JSON salvo
    try:
        with open(path, "r", encoding="utf-8") as f:
            transcricao: Dict = json.load(f)
    except Exception as e:
        raise HTTPException(status_code=500, detail={
            "code": "erro_leitura_json",
            "message": f"Falha ao ler {document_name}: {e}"
        })

    # 2) Extrai texto
    texto = transcricao.get("texto", "")
    if not texto:
        raise HTTPException(status_code=422, detail={
            "code": "sem_texto",
            "message": "Transcrição não contém o campo 'texto' ou está vazio."
        })

    # 3) Avalia com seu modelo (mantendo sua função atual)
    try:
        resultados = _avaliar_texto_modelo(texto, transcricao)
    except Exception as exc:
        EVALUATIONS_TOTAL.labels(status="error").inc()
        logger.error({
            "event": "evaluation_error",
            "document_name": document_name,
            "error": str(exc),
            "correlationId": correlation_id,
        })
        raise

    # 4) Extrai e normaliza a nota para gravar no JSON
    def _extrair_nota(metricas) -> float | None:
        # pode vir dict {"nota_atendimento": ...} ou float
        if isinstance(metricas, (int, float)):
            val = float(metricas)
        elif isinstance(metricas, dict):
            val = metricas.get("nota_atendimento")
            if not isinstance(val, (int, float)):
                return None
            val = float(val)
        else:
            return None

        # se estiver em 0–1, converte para 0–10
        if 0.0 <= val <= 1.0:
            val = val * 10.0

        return round(val, 1)

    nota_final = _extrair_nota(resultados)

    # 5) Atualiza o dicionário e sobrescreve o arquivo
    if nota_final is not None:
        transcricao["nota"] = nota_final
        try:
            with open(path, "w", encoding="utf-8") as f:
                json.dump(transcricao, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise HTTPException(status_code=500, detail={
                "code": "erro_escrita_json",
                "message": f"Falha ao escrever {document_name}: {e}"
            })
        try:
            # Observa distribuição de notas (0-10)
            EVALUATION_SCORE.observe(float(nota_final))
        except Exception:
            pass

    # 6) Resposta da API
    duration_s = time.time() - start_time
    EVALUATION_DURATION_SECONDS.observe(duration_s)
    EVALUATIONS_TOTAL.labels(status="success").inc()
    # E2E: calcula com base no início registrado no /stt/transcribe
    try:
        with _pipeline_lock:
            ts0 = _pipeline_start_ts.pop(document_name, None)
        if ts0 is not None:
            PIPELINE_E2E.observe(time.time() - ts0)
    except Exception:
        pass

    logger.info({
        "event": "evaluation_completed",
        "document_name": document_name,
        "durationMs": int(duration_s * 1000),
        "nota": nota_final,
        "correlationId": correlation_id,
    })

    return {
        "status": "avaliado",
        "document_name": document_name,
        "metricas": resultados,
        "nota": nota_final
    }