from __future__ import annotations
from fastapi import APIRouter, HTTPException, Request, status
from pydantic import BaseModel
import os, json, time, uuid, logging, sys

from app.config import TRANSCRICOES_DIR
from app.api.common.metrics import (
    EVALUATIONS_IN_FLIGHT, EVALUATIONS_TOTAL, EVALUATION_DURATION_SECONDS,
    EVALUATION_SCORE, PIPELINE_E2E
)
from app.api.common.utils import pipeline_pop_start

# Garante que "src" esteja no PYTHONPATH para importar src/nlp_model/*
# (não mexemos no código do modelo)
sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
)

logger = logging.getLogger("app")
router = APIRouter(prefix="/nlp", tags=["nlp"])

class EvaluateRequest(BaseModel):
    document_name: str

def _avaliar_texto_modelo(texto: str, transcricao_dict: dict) -> dict:
    """
    Usa a função real prever_nota_atendimento() de src/nlp_model/evaluator.py.
    """
    try:
        from nlp_model.evaluator import prever_nota_atendimento
    except Exception as e:
        return {"erro": "import_failed", "detalhe": f"Falha ao importar o modelo: {e}"}

    novo_atendimento = {
        "texto": texto,
        "raw": transcricao_dict,
    }
    try:
        nota = prever_nota_atendimento(
            novo_atendimento=novo_atendimento,
            channel="0800",
            models=None,
            alpha_ml=0.6,
            penalty_must=0.20
        )
    except Exception as e:
        return {"erro": "exec_failed", "detalhe": f"Falha ao executar modelo: {e}"}

    if isinstance(nota, (int, float)):
        return {"nota_atendimento": float(nota)}
    elif isinstance(nota, dict):
        return nota
    else:
        return {"nota_atendimento": None, "observacoes": "Retorno do modelo não reconhecido"}

@router.post("/evaluate")
async def avaliar_transcricao(payload: EvaluateRequest, request: Request):
    t0 = time.time()
    correlation_id = request.headers.get("X-Request-Id", str(uuid.uuid4()))
    EVALUATIONS_IN_FLIGHT.inc()
    logger.info({
        "event": "evaluation_started",
        "document_name": payload.document_name,
        "correlationId": correlation_id,
        "path": "/nlp/evaluate",
        "method": "POST",
    })

    document_name = payload.document_name
    path = os.path.join(TRANSCRICOES_DIR, document_name)
    if not os.path.isfile(path):
        EVALUATIONS_TOTAL.labels(status="error").inc()
        EVALUATIONS_IN_FLIGHT.dec()
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={
            "code": "nao_encontrado",
            "message": f"{document_name} não encontrado em {TRANSCRICOES_DIR}"
        })

    # 1) Carrega JSON salvo
    try:
        with open(path, "r", encoding="utf-8") as f:
            transcricao = json.load(f)
    except Exception as e:
        EVALUATIONS_TOTAL.labels(status="error").inc()
        EVALUATIONS_IN_FLIGHT.dec()
        raise HTTPException(status_code=500, detail={
            "code": "erro_leitura_json", "message": f"Falha ao ler {document_name}: {e}"
        })

    # 2) Extrai texto
    texto = transcricao.get("texto", "")
    if not texto:
        EVALUATIONS_TOTAL.labels(status="error").inc()
        EVALUATIONS_IN_FLIGHT.dec()
        raise HTTPException(status_code=422, detail={
            "code": "sem_texto", "message": "Transcrição não contém o campo 'texto' ou está vazio."
        })

    # 3) Avalia com modelo (sem alterar nada no src/nlp_model/*)
    resultados = _avaliar_texto_modelo(texto, transcricao)

    # 4) Normaliza nota (0–10) e persiste no mesmo JSON
    def _extrair_nota(metricas) -> float | None:
        if isinstance(metricas, (int, float)):
            val = float(metricas)
        elif isinstance(metricas, dict):
            val = metricas.get("nota_atendimento")
            if not isinstance(val, (int, float)):
                return None
            val = float(val)
        else:
            return None
        if 0.0 <= val <= 1.0:
            val *= 10.0
        return round(val, 1)

    nota_final = _extrair_nota(resultados)
    if nota_final is not None:
        transcricao["nota"] = nota_final
        try:
            with open(path, "w", encoding="utf-8") as f:
                json.dump(transcricao, f, ensure_ascii=False, indent=2)
        except Exception as e:
            EVALUATIONS_TOTAL.labels(status="error").inc()
            EVALUATIONS_IN_FLIGHT.dec()
            raise HTTPException(status_code=500, detail={
                "code": "erro_escrita_json", "message": f"Falha ao escrever {document_name}: {e}"
            })
        try:
            EVALUATION_SCORE.observe(float(nota_final))
        except Exception:
            pass

    # Métricas e E2E
    dur = time.time() - t0
    EVALUATION_DURATION_SECONDS.observe(dur)
    EVALUATIONS_TOTAL.labels(status="success").inc()

    try:
        ts0 = pipeline_pop_start(document_name)
        if ts0 is not None:
            PIPELINE_E2E.observe(time.time() - ts0)
    except Exception:
        pass

    logger.info({
        "event": "evaluation_completed",
        "document_name": document_name,
        "durationMs": int(dur * 1000),
        "nota": nota_final,
        "correlationId": correlation_id,
    })

    EVALUATIONS_IN_FLIGHT.dec()
    return {
        "status": "avaliado",
        "document_name": document_name,
        "metricas": resultados,
        "nota": nota_final
    }
