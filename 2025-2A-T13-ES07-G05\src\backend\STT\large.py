import whisper
import ffmpeg

model = whisper.load_model('large')
result = model.transcribe('comgas.mp3')

confianca_media = sum(seg['avg_logprob'] for seg in result['segments']) / len(result['segments'])
duracao_total = result['segments'][-1]['end'] if result['segments'] else 0

print('Modelo utilizado: large')
print(f"\nConfiança média: {confianca_media:.4f}")
print(f"Duração: {duracao_total:.2f} segundos")
print(f"Segmentos: {len(result['segments'])}")

print("Texto transcrito:")
print(result['text'])