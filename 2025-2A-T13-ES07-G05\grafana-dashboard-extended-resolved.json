{"dashboard": {"title": "Carbon - Pipeline Transcrição/NLP (Extended)", "schemaVersion": 38, "version": 2, "panels": [{"type": "timeseries", "title": "Transcrições/s (1m)", "fieldConfig": {"defaults": {"custom": {"axisSoftMin": 0, "axisSoftMax": 5}, "unit": "ops"}}, "targets": [{"expr": "sum(rate(app_transcriptions_total{status=\"success\"}[1m]))", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"type": "timeseries", "title": "Avaliações/s (1m)", "fieldConfig": {"defaults": {"custom": {"axisSoftMin": 0, "axisSoftMax": 5}, "unit": "ops"}}, "targets": [{"expr": "sum(rate(app_evaluations_total{status=\"success\"}[1m]))", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"type": "timeseries", "title": "Erro% (5m)", "fieldConfig": {"defaults": {"custom": {"axisSoftMin": 0, "axisSoftMax": 0.3}, "unit": "percentunit"}}, "targets": [{"expr": "sum(rate(app_transcriptions_total{status=\"error\"}[5m])) / sum(rate(app_transcriptions_total[5m]))", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 6, "w": 8, "x": 0, "y": 8}}, {"type": "timeseries", "title": "P50/P90/P99 Transcrição (s)", "fieldConfig": {"defaults": {"unit": "s", "custom": {"axisSoftMin": 0, "axisSoftMax": 30}}}, "targets": [{"expr": "histogram_quantile(0.5, sum(rate(app_transcription_duration_seconds_bucket[5m])) by (le))", "legendFormat": "p50", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}, {"expr": "histogram_quantile(0.9, sum(rate(app_transcription_duration_seconds_bucket[5m])) by (le))", "legendFormat": "p90", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}, {"expr": "histogram_quantile(0.99, sum(rate(app_transcription_duration_seconds_bucket[5m])) by (le))", "legendFormat": "p99", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 6, "w": 8, "x": 8, "y": 8}}, {"type": "timeseries", "title": "P50/P90/P99 Avaliação (s)", "fieldConfig": {"defaults": {"unit": "s", "custom": {"axisSoftMin": 0, "axisSoftMax": 10}}}, "targets": [{"expr": "histogram_quantile(0.5, sum(rate(app_evaluation_duration_seconds_bucket[5m])) by (le))", "legendFormat": "p50", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}, {"expr": "histogram_quantile(0.9, sum(rate(app_evaluation_duration_seconds_bucket[5m])) by (le))", "legendFormat": "p90", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}, {"expr": "histogram_quantile(0.99, sum(rate(app_evaluation_duration_seconds_bucket[5m])) by (le))", "legendFormat": "p99", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 6, "w": 8, "x": 16, "y": 8}}, {"type": "stat", "title": "In-flight: Transcrições", "targets": [{"expr": "app_transcriptions_in_flight", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 5, "w": 6, "x": 0, "y": 14}}, {"type": "stat", "title": "In-flight: <PERSON><PERSON><PERSON><PERSON><PERSON>", "targets": [{"expr": "app_evaluations_in_flight", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 5, "w": 6, "x": 6, "y": 14}}, {"type": "timeseries", "title": "E2E Pipeline (s)", "fieldConfig": {"defaults": {"unit": "s", "custom": {"axisSoftMin": 0, "axisSoftMax": 90}}}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(app_pipeline_end_to_end_seconds_bucket[5m])) by (le))", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 6, "w": 12, "x": 12, "y": 14}}, {"type": "timeseries", "title": "<PERSON><PERSON> (Transcrições/s)", "fieldConfig": {"defaults": {"custom": {"axisSoftMin": 0, "axisSoftMax": 5}, "unit": "ops"}}, "targets": [{"expr": "sum by (type) (rate(app_transcriptions_total_by_type{status=\"success\"}[1m]))", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 6, "w": 12, "x": 0, "y": 19}}, {"type": "timeseries", "title": "Segmentos por Transcrição (p90)", "fieldConfig": {"defaults": {"custom": {"axisSoftMin": 0, "axisSoftMax": 20}}}, "targets": [{"expr": "histogram_quantile(0.9, sum(rate(app_transcription_segments_bucket[5m])) by (le))", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 6, "w": 12, "x": 12, "y": 19}}, {"type": "timeseries", "title": "<PERSON><PERSON><PERSON><PERSON> Webhook (P95)", "fieldConfig": {"defaults": {"unit": "s", "custom": {"axisSoftMin": 0, "axisSoftMax": 5}}}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(app_webhook_latency_seconds_bucket[5m])) by (le))", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 6, "w": 12, "x": 0, "y": 25}}, {"type": "timeseries", "title": "Distribuição de Notas (P95)", "fieldConfig": {"defaults": {"unit": "short", "custom": {"axisSoftMin": 0, "axisSoftMax": 10}}}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(app_evaluation_score_bucket[5m])) by (le))", "datasource": {"type": "prometheus", "uid": "fez7i8foqu8e8c"}}], "gridPos": {"h": 6, "w": 12, "x": 12, "y": 25}}], "time": {"from": "now-1h", "to": "now"}}, "overwrite": true}