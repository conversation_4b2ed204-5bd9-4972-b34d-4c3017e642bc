version: '3.8'
services:
  mongodb:
    image: mongo:6.0
    container_name: mongodb_g5
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_INITDB_DATABASE}
    volumes:
      - ./data:/data/db

  rabbitmq:
    image: rabbitmq:3.13-management
    ports:
      - "5672:5672"
      - "15672:15672"
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  mongoimport:
    image: mongo:6.0
    depends_on:
      - mongodb
    volumes:
      - ./import:/import
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_INITDB_DATABASE}
    entrypoint: [ "bash", "/import/import.sh" ]

  prometheus:
    image: prom/prometheus:v2.55.1
    container_name: prometheus
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - --config.file=/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    depends_on:
      - mongodb

  grafana:
    image: grafana/grafana:11.2.0
    container_name: grafana_g5
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - ./grafana:/var/lib/grafana
    depends_on:
      - prometheus

  backend:
    build:
      context: ./src/backend
      dockerfile: Dockerfile
    container_name: backend_g5
    environment:
      # Ajuste estas variáveis conforme seu config.py / .env
      PYTHONPATH: /app
      TRANSCRICOES_DIR: /app/data/transcricoes
      # Por padrão, o webhook aponta para o próprio serviço backend
      EVAL_WEBHOOK_URL: http://backend:8000/nlp/evaluate
      MONGO_URI: ${MONGO_URI}
      MONGO_DB: ${MONGO_DB}
      MONGO_COLLECTION: ${MONGO_COLLECTION}
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASS: guest
      RABBITMQ_VHOST: /
      RABBITMQ_QUEUE: transcription.audio
    volumes:
      - ./src/backend/app:/app/app
      - ./data/transcricoes:/app/data/transcricoes
    ports:
      - "8000:8000"
    depends_on:
      - mongodb
    healthcheck:
      test: ["CMD", "curl", "-fsS", "http://localhost:8000/health"]
      interval: 15s
      timeout: 5s
      retries: 5

  consumer:
    build:
      context: ./src/project
      dockerfile: Dockerfile.consumer
    env_file:
      - ./src/project/.env
    environment:
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASS: guest
      RABBITMQ_QUEUE: transcription.audio
      WHISPER_MODEL: tiny
      PYTHONUNBUFFERED: "1"
    depends_on:
      rabbitmq:
        condition: service_healthy
    restart: unless-stopped
