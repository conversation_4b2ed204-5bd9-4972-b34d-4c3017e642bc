import os
import pika
from dotenv import load_dotenv

load_dotenv()

def get_connection():
    host = os.getenv("RABBITMQ_HOST", "localhost")
    port = int(os.getenv("RABBITMQ_PORT", "5672"))
    user = os.getenv("RABBITMQ_USER", "guest")
    password = os.getenv("RABBITMQ_PASS", "guest")

    params = pika.ConnectionParameters(
        host=host,
        port=port,
        credentials=pika.PlainCredentials(user, password),
        heartbeat=30,
        blocked_connection_timeout=30,
        socket_timeout=30,
    )
    return pika.BlockingConnection(params)

def get_channel(connection):
    return connection.channel()

    