FROM python:3.12-slim
ENV PYTHONDONTWRITEBYTECODE=1 PYTHONUNBUFFERED=1 PYTHONPATH=/app
RUN apt-get update && apt-get install -y --no-install-recommends ffmpeg && rm -rf /var/lib/apt/lists/*
WORKDIR /app
RUN pip install -U pip setuptools wheel \
 && pip install pika python-dotenv openai-whisper \
 && pip install torch --index-url https://download.pytorch.org/whl/cpu
COPY . /app
CMD ["python", "receiver/consumer.py"]