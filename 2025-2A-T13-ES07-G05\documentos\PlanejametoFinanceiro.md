

# 1. Planejamento Financeiro do Projeto (MVP)

O projeto tem como objetivo automatizar a avaliação da qualidade de atendimentos telefônicos da Comgás usando Processamento de Linguagem Natural (PLN). A solução transcreve áudios, avalia o conteúdo das conversas e gera um score de qualidade. O objetivo é fornecer ao cliente um dashboard com KPIs e insights, melhorando o processo de auditoria e a tomada de decisão.

O orçamento a seguir abrange o desenvolvimento do **<PERSON><PERSON><PERSON> <PERSON> (MVP)**, que será entregue em **10 semanas**, focando em uma equipe enxuta e uma infraestrutura econômica para validação técnica e de mercado.

-----

### **1.1. Descrição Breve do Projeto**

O escopo do MVP, a ser entregue em 10 semanas, consiste em um sistema de ponta a ponta para a avaliação de áudios de atendimentos. As principais entregas são:

  * **API de Ingestão e Transcrição:** Recebe arquivos de áudio, os transcreve usando o modelo Whisper e armazena os resultados.
  * **Mecanismo de Análise de Qualidade:** Utiliza PLN para avaliar critérios pré-definidos (cordialidade, aderência a roteiro, interrupções, etc.) e calcula um score de qualidade (0-10).
  * **Dashboard Executivo:** Interface construída com React Native para visualização dos resultados, incluindo métricas, filtros e detalhes das chamadas, com a vantagem de ser uma solução multi-plataforma.
  * **Infraestrutura Mínima:** Execução local via Docker, permitindo a validação do fluxo de trabalho sem custos de infraestrutura de nuvem em produção.
  * **Testes:** Cobertura de testes unitários, funcionais e de integração para garantir a estabilidade das funcionalidades críticas.

-----

### **1.2. Custos do Projeto**

#### **Equipe de Desenvolvimento**

A equipe foi dimensionada para ser mínima, priorizando especialistas multidisciplinares capazes de atuar em diferentes frentes do projeto. A dedicação de cada membro reflete a alocação de tempo necessária para cumprir o escopo de **10 semanas (aproximadamente 2.5 meses)**.

| Cargo | Salário Mensal | Dedicação | Período | Total 2.5 meses |
| :--- | :--- | :--- | :--- | :--- |
| Cientista de Dados Sênior | R$ 17.000 | 100% | 2.5 meses | R$ 42.500 |
| Engenheiro de Backend Sênior | R$ 15.000 | 80% | 2.5 meses | R$ 30.000 |
| Engenheiro de Frontend Sênior | R$ 14.000 | 60% | 2.5 meses | R$ 21.000 |
| Analista de DevOps Sênior | R$ 14.500 | 50% | 2.5 meses | R$ 18.125 |
| **Total Equipe** | | | | **R$ 111.625** |

#### **Materiais, Insumos e Licenças**

Os custos abaixo representam as ferramentas e licenças essenciais para o desenvolvimento do MVP, levando em conta a duração de **10 semanas**. Como o React Native é uma tecnologia open source, não há custos de licenças associados diretamente a ele, o que reflete a escolha por uma stack econômica.

| Item | Quantidade | Custo Mensal (BRL) | Total 2.5 meses (BRL) |
| :--- | :--- | :--- | :--- |
| GitHub Pro | 4 usuários | R$ 115,50 | R$ 288,75 |
| MongoDB Atlas (para dev) | M0 Cluster | R$ 0 | R$ 0 |
| **Total Ferramentas** | | | **R$ 288,75** |

#### **Infraestrutura Cloud Mínima (Desenvolvimento/Teste)**

A infraestrutura do MVP foi projetada para ser **econômica**, utilizando apenas os recursos essenciais para desenvolvimento e teste dos algoritmos. O uso da nuvem se limita a testes de validação e ao processamento do modelo Whisper.

| Serviço AWS | Custo Mensal (BRL) | Total 2.5 meses (BRL) |
| :--- | :--- | :--- |
| EC2 Development | R$ 269,83 | R$ 674,58 |
| SageMaker (para Whisper) | R$ 825,00 | R$ 2.062,50 |
| S3 Storage | R$ 31,62 | R$ 79,05 |
| **Total Infraestrutura** | | **R$ 2.816,13** |

-----

### **1.3. Despesas Operacionais Associadas ao Projeto**

As despesas operacionais para o MVP são estimadas em um valor fixo mensal, cobrindo o mínimo necessário para a gestão e execução do projeto durante o período de 10 semanas. O custo de marketing, em particular, é baixo porque se concentra em ações de **marketing de conteúdo e prospecção direta (outbound)**, que são mais focadas em um ambiente B2B e não exigem grandes investimentos em mídia paga.

| Atividade | Custo Mensal | Total 2.5 meses |
| :--- | :--- | :--- |
| Gerenciamento de Projeto | R$ 1.500 | R$ 3.750 |
| Suporte e Atendimento ao Cliente | R$ 500 | R$ 1.250 |
| Marketing e Divulgação | R$ 500 | R$ 1.250 |
| **Total Despesas Operacionais** | **R$ 2.500** | **R$ 6.250** |

-----

### **1.4. Carga Tributária (Impostos)**

A carga tributária será calculada com base no enquadramento fiscal como uma **empresa de serviços de tecnologia** no **Simples Nacional**. Esse regime simplificado é uma escolha estratégica para startups e empresas de pequeno porte devido à sua gestão descomplicada e alíquotas reduzidas.

O Simples Nacional consolida diversos impostos federais (IRPJ, CSLL, PIS/Pasep, COFINS, IPI), estaduais (ICMS) e municipais (ISS) em uma única guia de pagamento. No nosso caso, como se trata de um projeto de desenvolvimento de software, a atividade se enquadra no **Anexo III** do Simples Nacional. Para a simulação, consideramos a **alíquota inicial de 6%** sobre a receita bruta total, que é a faixa mais baixa de tributação.

Essa abordagem não apenas simplifica o cálculo, mas também reflete uma prática de mercado comum para viabilizar projetos com orçamentos mais competitivos.

-----

### **1.5. Margem de Lucro Desejada**

Definimos uma margem de lucro de **20%** sobre o valor do projeto, um percentual comum em projetos de desenvolvimento de software e consultoria. A **definição desse valor** não é arbitrária; ela foi planejada para que a empresa possa:

  * **Criar uma reserva de contingência** para cobrir custos não previstos, como a necessidade de mais horas de trabalho para calibração de modelos ou a mitigação de riscos técnicos.
  * **Garantir a viabilidade financeira da equipe** e da empresa no longo prazo, permitindo reinvestimento em pesquisa, desenvolvimento e capacitação.
  * **Oferecer um retorno justo** sobre o esforço intelectual e o risco assumido na execução do projeto.

-----

### **1.6. Preço Final do Projeto**

#### **Consolidação dos Custos Base**

| Categoria | Valor |
| :--- | :--- |
| **Custos de Pessoal** | R$ 111.625,00 |
| **Licenças e Ferramentas** | R$ 288,75 |
| **Infraestrutura Cloud** | R$ 2.816,13 |
| **Despesas Operacionais** | R$ 6.250,00 |
| **SUBTOTAL (Custos Base)** | **R$ 120.979,88** |

#### **Cálculo Final com Margem e Impostos**

| Componente | Cálculo | Valor |
| :--- | :--- | :--- |
| **Custos Base** | Somatória dos custos | R$ 120.979,88 |
| **Margem de Lucro (20%)** | R$ 120.979,88 × 0,20 | R$ 24.195,98 |
| **Valor com Lucro** | R$ 120.979,88 + R$ 24.195,98 | R$ 145.175,86 |
| **Impostos (6%)** | R$ 145.175,86 × 0,06 | R$ 8.710,55 |
| **VALOR TOTAL FINAL** | R$ 145.175,86 + R$ 8.710,55 | **R$ 153.886,41** |

#### **Composição do Preço Final**

O valor final de **R$ 153.886,41** para o MVP de 10 semanas é composto da seguinte forma:

  * **Custos Base:** R$ 120.979,88 (**78,6%**)
  * **Margem de Lucro (20%):** R$ 24.195,98 (**15,7%**)
  * **Impostos (6%):** R$ 8.710,55 (**5,7%**)

O orçamento proposto é um valor plausível para um projeto de 10 semanas de duração, com as expertises e tecnologias mencionadas.

-----

# 2. Planejamento Financeiro do Projeto Escalável (12 meses)

Esta seção apresenta o planejamento financeiro do projeto implementado, ou seja, a solução completa de automação de avaliação da qualidade de atendimentos telefônicos operando em ambiente de produção durante 12 meses. Esta fase contempla uma equipe de sustentação, infraestrutura robusta e escalável, e todos os componentes necessários para processar o volume-alvo de 30.000 ligações/mês.

### **2.1. Descrição Breve do Projeto**

O projeto em escala foca na operação, manutenção e melhoria contínua da solução já validada na fase MVP. As principais atividades e componentes a serem orçados para este período de **12 meses** incluem:

  * **Infraestrutura de Produção:** Implementação em AWS, utilizando serviços `serverless` e gerenciados como Lambda, API Gateway e SageMaker para garantir alta disponibilidade e escalabilidade.
  * **Suporte e Manutenção:** Equipe dedicada para monitoramento contínuo, solução de bugs, atualizações de segurança e atendimento ao cliente.
  * **Melhoria Contínua:** Evolução da plataforma com base em feedback do cliente e análise de dados, incluindo o aprimoramento dos modelos de PLN.
  * **Segurança e Compliance:** Implementação de medidas de segurança avançadas para proteger os dados sensíveis dos clientes (LGPD), como criptografia e gerenciamento de acessos.

-----

### **2.2. Custos do Projeto**

#### **Equipe de Produção**

A equipe foi dimensionada para garantir operação contínua, manutenção evolutiva e suporte técnico especializado durante os 12 meses de operação.

| Cargo | Salário Mensal | Quantidade | Dedicação | Total 12 meses |
| :--- | :--- | :--- | :--- | :--- |
| Cientista de Dados Sênior | R$ 17.000 | 1 | 100% | R$ 204.000 |
| Engenheiro de Backend Sênior | R$ 15.000 | 1 | 100% | R$ 180.000 |
| Engenheiro de Frontend Sênior | R$ 14.000 | 1 | 100% | R$ 168.000 |
| Analista de DevOps/SRE | R$ 14.500 | 1 | 100% | R$ 174.000 |
| Gerente de Produto | R$ 13.000 | 1 | 100% | R$ 156.000 |
| Analista de Suporte | R$ 8.500 | 1 | 100% | R$ 102.000 |
| **Total Equipe** | | | | **R$ 984.000** |

#### **Infraestrutura de Produção (AWS)**

Infraestrutura robusta projetada para alta disponibilidade, escalabilidade e performance necessárias para processar 30.000 ligações por mês.

| Serviço AWS | Custo Mensal (BRL) | Total 12 meses (BRL) |
| :--- | :--- | :--- |
| **API Gateway** | R$ 1.250 | R$ 15.000 |
| **AWS Lambda** | R$ 100 | R$ 1.200 |
| **S3 Storage** | R$ 450 | R$ 5.400 |
| **DynamoDB** | R$ 1.200 | R$ 14.400 |
| **SageMaker (Whisper)** | R$ 20.000 | R$ 240.000 |
| **CloudWatch (Monitoramento)** | R$ 1.500 | R$ 18.000 |
| **Total Infraestrutura** | | **R$ 294.000** |

#### **Materiais, Insumos e Licenças de Produção**

Ferramentas e licenças necessárias para a operação e o monitoramento da solução em um ambiente de larga escala.

| Item | Custo Mensal (BRL) | Total 12 meses (BRL) |
| :--- | :--- | :--- |
| GitHub Enterprise | R$ 840 | R$ 10.080 |
| **Total Ferramentas** | | **R$ 10.080** |

-----

### **2.3. Despesas Operacionais Associadas ao Projeto**

As despesas operacionais em um cenário escalável são mais abrangentes e incluem serviços especializados e custos administrativos para a manutenção do negócio. O custo de marketing, por ser uma fase de produção, aumenta para R$ 1.500 mensais, destinado a ações de **marketing digital B2B**, como campanhas de prospecção e conteúdo em plataformas como LinkedIn, além da manutenção de ferramentas de automação e CRM.

| Atividade | Custo Mensal (BRL) | Total 12 meses (BRL) |
| :--- | :--- | :--- |
| Gerenciamento de Projeto | R$ 2.500 | R$ 30.000 |
| Suporte e Atendimento ao Cliente | R$ 2.000 | R$ 24.000 |
| Marketing e Divulgação | R$ 1.500 | R$ 18.000 |
| Contabilidade e Jurídico | R$ 2.000 | R$ 24.000 |
| **Total Despesas Operacionais** | **R$ 8.000** | **R$ 96.000** |

-----

### **2.4. Carga Tributária (Impostos)**

Para um projeto em escala, mantemos o enquadramento no **Simples Nacional**, mas consideramos que o faturamento elevado move a empresa para uma faixa de alíquota superior.

O Simples Nacional é um regime progressivo, com alíquotas que aumentam à medida que o faturamento da empresa cresce. A fase inicial do MVP se enquadra na menor alíquota (6% sobre o faturamento), mas a operação em escala para um cliente como a Comgás, processando 30.000 ligações mensais, resultaria em um faturamento anual que posicionaria a empresa em uma faixa de tributação mais alta, dentro do mesmo regime. Para essa estimativa, optamos por uma alíquota de **15%**, que é uma estimativa conservadora e realista para uma empresa de tecnologia com faturamento nesse patamar. Essa abordagem garante que a precificação reflita a real carga fiscal do negócio em sua fase de produção.

-----

### **2.5. Margem de Lucro Desejada**

Para um projeto em escala, a margem de lucro pode ser ligeiramente ajustada para refletir a previsibilidade e a otimização de custos da operação. Definimos uma margem de lucro de **15%** sobre o valor final. A **definição desse valor** foi strategicamente planejada para:

  * **Manter a competitividade do preço no mercado**, mesmo com o aumento dos custos de infraestrutura e equipe.
  * **Garantir o reinvestimento em inovação**, como a pesquisa de novos modelos de PLN ou a expansão de funcionalidades, assegurando a evolução contínua da plataforma.
  * **Oferecer um retorno financeiro sólido** e justificável para o esforço de implementação e manutenção do projeto, validando o modelo de negócio a longo prazo.

-----

### **2.6. Preço Final do Projeto**

#### **Consolidação dos Custos Base**

| Categoria | Valor |
| :--- | :--- |
| **Custos de Pessoal** | R$ 984.000 |
| **Infraestrutura Cloud** | R$ 294.000 |
| **Licenças e Ferramentas** | R$ 10.080 |
| **Despesas Operacionais** | R$ 96.000 |
| **SUBTOTAL (Custos Base)** | **R$ 1.384.080** |

#### **Cálculo Final com Margem e Impostos**

| Componente | Cálculo | Valor |
| :--- | :--- | :--- |
| **Custos Base** | Somatória dos custos | R$ 1.384.080 |
| **Margem de Lucro (15%)** | R$ 1.384.080 × 0,15 | R$ 207.612 |
| **Valor com Lucro** | R$ 1.384.080 + R$ 207.612 | R$ 1.591.692 |
| **Impostos (15%)** | R$ 1.591.692 × 0,15 | R$ 238.753,80 |
| **VALOR TOTAL FINAL** | R$ 1.591.692 + R$ 238.753,80 | **R$ 1.830.445,80** |

#### **Composição do Preço Final**

O valor final de **R$ 1.830.445,80** para o projeto em escala de 12 meses é composto da seguinte forma:

  * **Custos Base:** R$ 1.384.080 (**75,6%**)
  * **Margem de Lucro (15%):** R$ 207.612 (**11,3%**)
  * **Impostos (15%):** R$ 238.753,80 (**13,1%**)

O orçamento proposto é um valor competitivo e viável para a operação da solução em larga escala, considerando os custos e o valor estratégico que a automação trará ao cliente.

-----


### **Referências**

  * **Salários:**
      * [Robert Half, Guia Salarial 2024](https://www.roberthalf.com.br/guia-salarial)
      * [Glassdoor, Salários Brasil](https://www.google.com/search?q=https://www.glassdoor.com.br/Salarios)
      * [Jooble, Salário Desenvolvedor React Native](https://br.jooble.org/salary/desenvolvedor-react-native)
  * **Custos de Software e Licenças:**
      * [GitHub Pricing](https://github.com/pricing)
      * [MongoDB Atlas Pricing](https://www.mongodb.com/pricing)
  * **Custos de Infraestrutura Cloud:**
      * [AWS SageMaker Pricing](https://aws.amazon.com/sagemaker/pricing/)
      * [AWS Lambda Pricing](https://aws.amazon.com/lambda/pricing/)
      * [AWS API Gateway Pricing](https://aws.amazon.com/api-gateway/pricing/)
      * [AWS DynamoDB Pricing](https://aws.amazon.com/dynamodb/pricing/)
  * **Impostos:**
      * [Receita Federal do Brasil, Simples Nacional](https://www.gov.br/receitafederal/pt-br/assuntos/orientacao-tributaria/cobrancas-e-intimacoes/orientacoes-para-regularizacao-de-pendencias-simples-nacional)
  * **Marketing B2B:**
      * [Quanto Custa o Marketing Digital](https://www.hostinger.com/br/tutoriais/quanto-custa-marketing-digital)
      * [Quanto Custa Anunciar no LinkedIn](https://business.linkedin.com/pt-br/marketing-solutions/ads/pricing)

