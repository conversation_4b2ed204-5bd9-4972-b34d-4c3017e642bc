<Table>
  <tr>
    <td><a href= "https://www.comgas.com.br/"><img src="./documentos/img/comgas.png" alt="Comgas" border="0"></td>
    <td>
      <a href= "https://www.inteli.edu.br/"><img src="./documentos/img/logo-Inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
    </td>
  </tr>
</table>

# Nome do Projeto: Carbon

## Nome do Grupo: Carbon

## Integrantes:

- <a href="https://www.linkedin.com/in/anacdejesus//">Ana Carolina de <PERSON> da <PERSON></a>
- <a href="https://www.linkedin.com/in/felipe-zillo-72b367247/"><PERSON></a>
- <a href="https://www.linkedin.com/in/fernandobertholdo/"><PERSON></a>
- <a href="https://www.linkedin.com/in/henrique-botti-6272571a0/"><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/karine-victoria//">Karine Victoria Rosa da Paixão</a>
- <a href="https://www.linkedin.com/in/natalycunha /">Nataly de Souza Cunha</a>
- <a href="https://www.linkedin.com/in/tainacortez/">Tainá de Paiva Cortez</a>


## Professores:
### Orientador(a) 
- <a href="https://www.linkedin.com/in/vanunes/">Vanessa Nunes</a>
### Instrutores
- <a href="https://www.linkedin.com/in/reginaldo-arakaki-9574222b/">Computação - Reginaldo Arakaki</a>
- <a href="https://www.linkedin.com/in/bryan-kano/">Computação - Bryan Kano Ferreira</a>
- <a href="https://www.linkedin.com/in/ovidio-netto/">Computação - Ovidio Lopes da Cruz Netto</a>
- <a href="https://www.linkedin.com/in/cristinagramani/">Matemática - Maria Cristina Nogueira Gramani</a>
- <a href="https://www.linkedin.com/in/lisane-valdo/">Negócios - Lisane Valdo</a>
- <a href="https://www.linkedin.com/in/francisco-escobar/">Design - Francisco Escobar</a> 
- <a href="https://www.linkedin.com/in/filipe-gon%C3%A7alves-08a55015b/">Liderança - Filipe Gonçalves</a>

## 📝 Descrição

Este projeto é uma solução inteligente desenvolvida para a Comgás, com foco na **análise automatizada de ligações de atendimento ao cliente** utilizando técnicas avançadas de **Processamento de Linguagem Natural (NLP)**. O objetivo principal é ampliar significativamente a amostragem de avaliações de qualidade, passando do atual índice de 1% (processo manual) para até 20%, de forma automatizada e escalável.

Além da análise, **o sistema incorpora um módulo de transcrição de chamadas** — eliminando a dependência de soluções externas — e **oferece uma visão consolidada dos dados em uma interface intuitiva**. A partir dessas transcrições, a plataforma não apenas exibe informações operacionais, mas também **gera insights estratégicos**, como correlação entre notas de avaliação, distribuição de casos de atendimento por região e identificação de padrões relevantes para a gestão da qualidade.

O projeto foi concebido com foco em eficiência, precisão e inteligência, permitindo que as equipes de qualidade e operação atuem de maneira mais proativa, com base em informações confiáveis e atualizadas, potencializando a tomada de decisão e a melhoria contínua no relacionamento com o cliente.

## 📝 LINKS

<a href="/documentos/Projeto.md">Link para a documentação geral</a> do projeto.

## 📁 Estrutura de pastas

```
📁 documentos/
   ├── img/                              # Imagens utilizadas na documentação
   ├── GestaoConfiguracao.md             # Documentação de gestão de configuração
   ├── GestaoProjeto.md                  # Documentação de gestão do projeto
   ├── GuiaPadroesDesenvolvimento.md     # Guia de padrões de desenvolvimento
   ├── Index.md                          # Índice da documentação
   └── Projeto.md                        # Documentação principal do projeto

📄 README.md                             # Arquivo principal do repositório
```


Dentre os arquivos presentes na raiz do projeto, definem-se:

- **README.md**: arquivo que serve como guia e explicação geral sobre o projeto (o mesmo que você está lendo agora).

- **documentos**: pasta que contém toda a documentação do projeto, incluindo guias, manuais e imagens utilizadas.

- **documentos/img**: pasta dedicada às imagens utilizadas na documentação, como diagramas, fluxogramas e ilustrações.

- **src**: pasta que conterá todo o código-fonte do sistema, incluindo backend, frontend, scripts de banco de dados, modelos de machine learning e APIs complementares.

## 💻 Configuração para desenvolvimento

Para configurar o desenvolvimento da aplicação, [instale o git](https://git-scm.com/downloads) e clone esse repositório em seu computador através do comando:

```
git clone https://github.com/Inteli-College/2025-2A-T13-ES07-G05
```

## 🔭 Observabilidade (Sprint 4)

Este projeto expõe métricas Prometheus em `/metrics` e logs estruturados em JSON.

### Eventos e métricas implementadas
- Eventos (logs): `transcription_started`, `transcription_saved`, `transcription_error`, `webhook_enqueued`, `webhook_error`, `evaluation_started`, `evaluation_completed`, `evaluation_error`.
- Métricas (Prometheus):
  - `app_transcriptions_total{status="success|error"}`
  - `app_transcription_duration_seconds` (histogram)
  - `app_evaluations_total{status="success|error"}`
  - `app_evaluation_duration_seconds` (histogram)
  - `app_webhook_failures_total`

### Subindo Prometheus e Grafana
1. Garanta o backend rodando localmente em `http://127.0.0.1:8000` (ou ajuste o alvo no `prometheus/prometheus.yml`).
2. Suba Prometheus e Grafana:
   ```bash
   docker compose up -d prometheus grafana
   ```
3. Acesse Prometheus em `http://localhost:9090` e consulte as métricas acima.
4. Acesse Grafana em `http://localhost:3000` (login `admin`/`admin`).
   - Adicione um Data Source do tipo Prometheus com URL `http://prometheus:9090`.
   - Crie um Dashboard e adicione painéis com as consultas:
     - `rate(app_transcriptions_total{status="success"}[5m])`
     - `histogram_quantile(0.95, sum(rate(app_transcription_duration_seconds_bucket[5m])) by (le))`
     - `rate(app_evaluations_total{status="success"}[5m])`
     - `histogram_quantile(0.95, sum(rate(app_evaluation_duration_seconds_bucket[5m])) by (le))`
     - `increase(app_webhook_failures_total[1h])`

### Teste ponta a ponta
1. Faça upload de um áudio pelo frontend (botão "Adicionar chamada") ou via curl:
   ```bash
   curl -F "audio_file=@/caminho/para/audio.mp3" http://127.0.0.1:8000/stt/transcribe
   ```
2. Aguarde a avaliação assíncrona (webhook) e confira `./data/transcricoes/transcricao_XXX.json` com a nota.
3. Veja as métricas em `http://localhost:9090` e construa gráficos no Grafana.

## 🗃 Histórico de lançamentos

* 0.1.0 - 15/08/2025
  * Entendimento do negócio
  * Especificação de requisitos funcionais e não funcionais
  * Gestão de projeto e configuração
* 0.2.0 - 29/08/2025
  * Design compreensivo e projeto visual
  * Estratégia técnica da solução
  * Gestão de projetos evolutiva
* 0.3.0 - 12/09/2025
  * Desenvolvimento e documentação técnica do projeto
  * Planejamento de testes sistêmicos
  * Gestão de projeto evolutiva
* 0.4.0 - 26/09/2025
  * Desenvolvimento e documentação técnica do projeto
  * Execução de testes sistêmicos
  * Gestão de projetos evolutiva e análise de viabilidade financeira
* 0.5.0 - 09/10/2025
  * Desenvolvimento de documentação técnica do projeto
  * Documentação técnica final
  * Gestão de projetos evolutiva e apresentação final

## 📋 Licença/License

<p xmlns:cc="http://creativecommons.org/ns#" xmlns:dct="http://purl.org/dc/terms/"><a property="dct:title" rel="cc:attributionURL" href="https://github.com/Inteli-College/2025-2A-T13-ES07-G05"><nome do projeto></a> by <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://github.com/Inteli-College/2025-2A-T13-ES07-G05">INTELI</a>, <a href="https://www.linkedin.com/in/anacdejesus/">Ana Carolina Pacheco</a>, <a href="https://www.linkedin.com/in/felipe-zillo-72b367247/">Felipe Zillo</a>, <a href="https://www.linkedin.com/in/fernandobertholdo/">Fernando Tavares Bertholdo</a>, <a href="https://www.linkedin.com/in/henrique-botti-6272571a0/">Henrique Botti</a>, <a href="https://www.linkedin.com/in/karine-victoria//">Karine Victoria Rosa da Paixão</a>, <a href="https://www.linkedin.com/in/natalycunha /">Nataly de Souza Cunha</a>, <a href="https://www.linkedin.com/in/tainacortez/">Tainá de Paiva Cortez</a> is licensed under <a href="http://creativecommons.org/licenses/by/4.0/?ref=chooser-v1" target="_blank" rel="license noopener noreferrer" style="display:inline-block;">Attribution 4.0 International<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/cc.svg?ref=chooser-v1"><img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/by.svg?ref=chooser-v1"></a></p>
