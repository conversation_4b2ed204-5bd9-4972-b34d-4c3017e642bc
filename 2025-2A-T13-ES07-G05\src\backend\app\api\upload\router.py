# app/api/routers.py
from __future__ import annotations 
from fastapi import APIRouter, UploadFile, File, status, HTTPException, Request
from mutagen.mp3 import MP3
from mutagen.wave import WAVE
from io import BytesIO
from app.config import ALLOWED_AUDIO_TYPES as allowed_types
from app.services.queue import enqueue_audio_for_transcription, QueuePublishError

router = APIRouter()

@router.post( 
    "/uploadaudio/", 
    responses={ 
        200: {"description": "Upload realizado com sucesso"}, 
        400: {"description": "Erro de validação do arquivo"}, 
        415: {"description": "Tipo de mídia não suportado"} 
    } 
)

async def upload_audio(request: Request, audio_file: UploadFile = File(...)):
    # Valida o tipo do arquivo
    if audio_file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail={
                "code": "Tipo de mídia não suportado",
                "message": f"Apenas arquivos MP3 ou WAV são permitidos. Recebido: {audio_file.content_type}."
            }
        )

    # Lê bytes do arquivo
    file_bytes = await audio_file.read()
    size_bytes = len(file_bytes or b"")
    if size_bytes == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"code": "Arquivo vazio", "message": "O arquivo de áudio está vazio."}
        )

    # Calcula duração via mutagen usando um buffer em memória
    bio = BytesIO(file_bytes)
    try:
        if audio_file.content_type == "audio/mpeg":
            audio = MP3(bio)
        else:
            audio = WAVE(bio)
        duration_seconds = float(audio.info.length)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"code": "Duração inválida", "message": "Não foi possível determinar a duração do áudio."}
        )

    # Envia para a fila
    client_ip = request.client.host if request.client else None
    try:
        message_id = enqueue_audio_for_transcription(
            file_bytes=file_bytes,
            filename=audio_file.filename,
            content_type=audio_file.content_type,
            duration_seconds=duration_seconds,
            client_ip=client_ip,
        )
    except QueuePublishError:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"code": "Fila indisponível", "message": "Não foi possível enfileirar o áudio para transcrição."}
        )

    # Resposta
    return {
        "Arquivo": audio_file.filename,
        "Duração (minutos)": round(duration_seconds / 60.0, 2),
        "Tipo de arquivo": audio_file.content_type,
        "Tamanho (bytes)": size_bytes,
        "Status": "Enfileirado para transcrição",
        "Id da mensagem": message_id,
    }