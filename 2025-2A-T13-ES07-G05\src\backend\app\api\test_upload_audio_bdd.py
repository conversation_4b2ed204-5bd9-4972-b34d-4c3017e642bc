import io
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
from http import HTTPStatus

import pytest
from pytest_bdd import given, when, then, scenarios, parsers
from fastapi.testclient import TestClient

# Garante que o diretório raiz do backend (src/backend) esteja no sys.path
BACKEND_ROOT = Path(__file__).resolve().parents[2]
if str(BACKEND_ROOT) not in sys.path:
    sys.path.insert(0, str(BACKEND_ROOT))

try:
    from main import app
except ImportError:
    from app.main import app


scenarios("upload_audio.feature")


@pytest.fixture
def client():
    return TestClient(app)


def _mock_audio(duration_seconds: float):
    audio = MagicMock()
    audio.info.length = duration_seconds
    return audio


@pytest.fixture
def mp3_success():
    return lambda _file: _mock_audio(207.0)


@pytest.fixture
def wav_success():
    return lambda _file: _mock_audio(150.0)


@pytest.fixture
def mp3_bad_duration():
    return lambda _file: _mock_audio(-5.0)


@given(parsers.cfparse('um arquivo de áudio válido "{filename}" do tipo "{content_type}"'), target_fixture="file_payload")
def given_valid_file(filename: str, content_type: str):
    return {"filename": filename, "content_type": content_type, "content": b"fake content" * 10}


@given(parsers.cfparse('um arquivo de áudio inválido "{filename}" do tipo "{content_type}"'), target_fixture="file_payload")
def given_invalid_file(filename: str, content_type: str):
    return {"filename": filename, "content_type": content_type, "content": b"content"}


@given(parsers.cfparse('um arquivo de áudio vazio "{filename}" do tipo "{content_type}"'), target_fixture="file_payload")
def given_empty_file(filename: str, content_type: str):
    return {"filename": filename, "content_type": content_type, "content": b""}


@given(parsers.cfparse('um arquivo de áudio com duração inválida "{filename}" do tipo "{content_type}"'), target_fixture="file_payload")
def given_bad_duration_file(filename: str, content_type: str):
    return {"filename": filename, "content_type": content_type, "content": b"fake content" * 10}


@when(parsers.cfparse('eu fizer POST para "{route}"'), target_fixture="response")
def do_post(client: TestClient, route: str, file_payload, mp3_success, wav_success, mp3_bad_duration):
    files = {"audio_file": (file_payload["filename"], io.BytesIO(file_payload["content"]), file_payload["content_type"])}
    content_type = file_payload["content_type"]
    filename = file_payload["filename"]

    if content_type == "audio/mpeg":
        side_effect = mp3_bad_duration if "bad_duration" in filename else mp3_success
        with patch("app.api.routers.MP3", side_effect=side_effect):
            return client.post(route, files=files)
    if content_type in ("audio/wav", "audio/x-wav"):
        with patch("app.api.routers.WAVE", side_effect=wav_success):
            return client.post(route, files=files)
    return client.post(route, files=files)


@then(parsers.cfparse('a resposta deve ter status {status:d}'))
def assert_status(response, status: int):
    assert response.status_code == status


@then(parsers.cfparse('o campo "{path}" deve ser "{expected}"'))
def assert_field_equals(response, path: str, expected: str):
    data = response.json()
    for key in path.split('.'):
        data = data[key]
    assert str(data) == expected


# Mocks por cenário


# Removido autouse; os patches são aplicados diretamente no step When


# Step específico para valores numéricos (evita colisão com o step de string)
@then(parsers.re(r'o campo "(?P<path>[^\"]+)" deve ser (?P<expected>-?\d+(?:\.\d+)?)'))
def assert_field_float(response, path: str, expected: str):
    data = response.json()
    for key in path.split('.'):
        data = data[key]
    assert float(data) == pytest.approx(float(expected))


