# src/nlp_model/evaluator.py
# Mó<PERSON><PERSON> limpo: somente o essencial para avaliar atendimentos via regras + (opcional) modelos ML.

from __future__ import annotations
import re
from typing import Dict, List, Optional
from unidecode import unidecode

# ---------------------------
# Critérios e seleção por canal
# ---------------------------

criteria_0800: List[Dict] = [
    {"code":"C01","name":"Saudação inicial adequada (0800)","type":"should","weight":1.0,"who":"agent",
     "regex": r"\b(ol[áa]|oi|bom dia|boa tarde|boa noite)\b"},
    {"code":"C02","name":"Identificação do agente (Comgás)","type":"should","weight":0.7,"who":"agent",
     "regex": r"\b(meu nome (é|e)|falo com|da comg[aá]s)\b"},
    {"code":"C03","name":"Solicitação de confirmação de dados","type":"must","weight":1.5,"who":"agent",
     "regex": r"\b(confirmar|confirme|poderia confirmar).*(dados|nome completo|data de nascimento)\b"},
    {"code":"C04","name":"Validação (cliente: nome + data)","type":"must","weight":1.8,"who":"user",
     "regex": r"\b(sou|meu nome (é|e))\b.*\b(0[1-9]|[12][0-9]|3[01])[\/\.\-](0[1-9]|1[0-2])[\/\.\-](19|20)\d{2}\b"},
    {"code":"C05","name":"Empatia / reconhecimento","type":"should","weight":0.8,"who":"agent",
     "regex": r"\b(entendo|compreendo|sinto muito|entendi sua preocupa[cç][aã]o)\b"},
    {"code":"C06","name":"Esclarece causa/diagnóstico","type":"should","weight":1.0,"who":"agent",
     "regex": r"\b(identifiquei|verifiquei|constat(ei|amos)|houve cobran[çc]a de m[eé]dia|causa)\b"},
    {"code":"C07","name":"Abre OS/protocolo","type":"must","weight":1.7,"who":"agent",
     "regex": r"\b(abrir|abrirei|abr(i|o) )\s*(uma )?(os|ordem de servi[cç]o|solicita[cç][aã]o|protocolo)|solicita[cç][aã]o aberta|protocolo\b"},
    {"code":"C08","name":"SLA/prazo comunicado","type":"must","weight":1.4,"who":"agent",
     "regex": r"\b(\d{1,2})\s*(dia|dias|hora|horas)\b.*(útil|úteis)?\b"},
    {"code":"C09","name":"Checagem de clareza","type":"should","weight":1.0,"who":"agent",
     "regex": r"\b(ficou claro|est[aá] claro|posso abrir a solicita[cç][aã]o)\b"},
    {"code":"C10","name":"Encerramento + oferta de ajuda","type":"should","weight":1.2,"who":"agent",
     "regex": r"\b(posso ajudar em (mais|algo mais)|mais alguma coisa)\b"},
    {"code":"C11","name":"Convite à pesquisa de satisfação","type":"should","weight":0.7,"who":"agent",
     "regex": r"\b(pesquisa de satisfa[cç][aã]o)\b"},
]

CRITERIA_BY_CHANNEL: Dict[str, List[Dict]] = {
    "0800": criteria_0800
}

def get_criteria(channel: str) -> List[Dict]:
    """Retorna a lista de critérios para o canal informado (default: 0800)."""
    return CRITERIA_BY_CHANNEL.get(channel, criteria_0800)

# ---------------------------
# Pré-processamento mínimo
# ---------------------------

def _by_speaker(transcricao: Dict) -> Dict[str, str]:
    """
    Separa e concatena falas por speaker (agent/user). Se não houver estrutura por mensagens,
    aplica fallback para o texto inteiro em todos os campos.
    """
    if isinstance(transcricao, dict) and 'messages' in transcricao:
        agent_texts, user_texts = [], []
        for msg in transcricao['messages']:
            spk = (msg.get('speaker') or '').lower().strip()
            txt = msg.get('text') or ''
            if spk == 'agent':
                agent_texts.append(txt)
            elif spk == 'user':
                user_texts.append(txt)
        agent = ' '.join(agent_texts)
        user  = ' '.join(user_texts)
        return {
            'agent': unidecode(agent.lower()),
            'user':  unidecode(user.lower()),
            'all':   unidecode((agent + ' ' + user).lower())
        }
    # fallback: texto único
    s = unidecode(str(transcricao or '').lower())
    return {'agent': s, 'user': s, 'all': s}

# ---------------------------
# Núcleo de avaliação
# ---------------------------

def evaluate_transcription(
    transcricao: Dict,
    criteria_list: List[Dict],
    models: Optional[Dict] = None,
    alpha_ml: float = 0.6,
    penalty_must: float = 0.20,
    debug: bool = False  # mantido por compatibilidade; não imprime nada aqui
) -> float:
    """
    Híbrido regras + ML (opcional). Resultado: nota 0–10 com penalização suave por MUST não atendido.
    - models["general_clf"]: Pipeline sklearn com predict_proba
    - models["general_reg"]: Pipeline sklearn com predict (0–10) ou função callable(texts)->np.ndarray/Sequence
    """
    texts = _by_speaker(transcricao)
    total_weight = sum(float(c['weight']) for c in criteria_list)
    if total_weight <= 0:
        return 0.0

    def ml_pos_proba(text_all: str) -> float:
        if not models:
            return 0.5

        # Classificador com predict_proba → usa probabilidade positiva
        clf = models.get("general_clf")
        if clf is not None:
            try:
                proba = clf.predict_proba([text_all])[0]
                return float(proba[1]) if len(proba) == 2 else float(max(proba))
            except Exception:
                pass

        # Regressor (0–10) → normaliza para 0–1
        reg = models.get("general_reg")
        if reg is not None:
            try:
                if callable(reg):
                    y = float(reg([text_all])[0])
                else:
                    y = float(reg.predict([text_all])[0])
                return float(max(0.0, min(10.0, y)) / 10.0)
            except Exception:
                pass

        return 0.5

    ml_pos = ml_pos_proba(texts["all"])

    raw_sum = 0.0
    must_fail_count = 0
    for c in criteria_list:
        who  = c["who"].lower()
        w    = float(c["weight"])
        patt = c["regex"]
        hay  = texts.get(who, texts["all"])

        rule = 1 if re.search(patt, hay) else 0
        crit_score = alpha_ml * ml_pos + (1 - alpha_ml) * rule
        raw_sum += crit_score * w

        if c["type"].lower() == "must" and rule == 0:
            must_fail_count += 1

    base_score = 10.0 * (raw_sum / total_weight)
    base_score = max(0.0, min(10.0, base_score))

    if must_fail_count > 0:
        penalty_multiplier = max(0.1, 1.0 - penalty_must * must_fail_count)
        final_score = base_score * penalty_multiplier
    else:
        final_score = base_score

    return round(max(0.0, min(10.0, final_score)), 2)

def prever_nota_atendimento(
    novo_atendimento: Dict,
    channel: str = "0800",
    models: Optional[Dict] = None,
    alpha_ml: float = 0.6,
    penalty_must: float = 0.20
) -> float:
    """
    Interface pública usada pela API.
    Espera um dicionário do atendimento (idealmente com 'messages' contendo {speaker,text}).
    """
    crit = get_criteria(channel)
    return evaluate_transcription(
        novo_atendimento,
        crit,
        models=models,
        alpha_ml=alpha_ml,
        penalty_must=penalty_must,
        debug=False
    )
