{"cells": [{"cell_type": "markdown", "metadata": {"id": "ltvj3_lWYOKH"}, "source": ["# Análise Comparativa de Modelos Whisper para Transcrição de Áudio em Português\n", "\n", "## Objetivo geral\n", "Este notebook tem como objetivo identificar o melhor modelo da biblioteca Whisper para transcrição de áudios em português, no contexto de atendimento da Comgás.\n", "\n", "## Metodologia\n", "- Foram testadas todas as versões disponíveis do Whisper: `tiny`, `base`, `small`, `medium`, `large`.\n", "- Foi utilizada uma amostra de áudio desenvolvida pela equipe simulando uma situação real de atendimento.\n", "- As avaliações consideraram precisão, velocidade e confiabilidade das transcrições.\n", "\n", "## Configuração recomendada\n", "Para garantir que o notebook rode de forma eficiente, siga estas instruções:\n", "\n", "1. **Ativar GPU no Colab**\n", "   - Menu: `Ambiente de execução → Alterar tipo de ambiente de execução → Acelerador de hardware → GPU`\n", "   - <PERSON><PERSON> permite que os modelos maiores (medium e large) rodem mais rápido.\n", "2. **<PERSON><PERSON> apenas um modelo por vez**\n", "   - Para evitar problemas de memória, carregue e transcreva um modelo por vez.\n", "3. **Limpar memória entre execuções**\n", "   - Use o seguinte bloco no início de cada célula que carrega um modelo:\n", "   ```python\n", "   import gc\n", "   import torch\n", "\n", "   try:\n", "       del model\n", "       gc.collect()\n", "       torch.cuda.empty_cache()\n", "   except:\n", "       pass\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "m-igz9LEEAd4", "outputId": "ba588a31-d6f6-4369-f8e7-942f5289229c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting git+https://github.com/openai/whisper.gitNote: you may need to restart the kernel to use updated packages.\n", "\n", "  Cloning https://github.com/openai/whisper.git to c:\\users\\<USER>\\appdata\\local\\temp\\pip-req-build-xzdykgmc\n", "  Resolved https://github.com/openai/whisper.git to commit c0d2f624c09dc18e709e37c2ad90c039a4eb72a2\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: more-itertools in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from openai-whisper==20250625) (10.8.0)\n", "Requirement already satisfied: numba in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from openai-whisper==20250625) (0.62.0)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from openai-whisper==20250625) (2.3.2)\n", "Requirement already satisfied: tiktoken in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from openai-whisper==20250625) (0.11.0)\n", "Requirement already satisfied: torch in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from openai-whisper==20250625) (2.8.0)\n", "Requirement already satisfied: tqdm in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from openai-whisper==20250625) (4.67.1)\n", "Requirement already satisfied: llvmlite<0.46,>=0.45.0dev0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from numba->openai-whisper==20250625) (0.45.0)\n", "Requirement already satisfied: regex>=2022.1.18 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from tiktoken->openai-whisper==20250625) (2025.9.18)\n", "Requirement already satisfied: requests>=2.26.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from tiktoken->openai-whisper==20250625) (2.32.5)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from requests>=2.26.0->tiktoken->openai-whisper==20250625) (3.4.3)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from requests>=2.26.0->tiktoken->openai-whisper==20250625) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from requests>=2.26.0->tiktoken->openai-whisper==20250625) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from requests>=2.26.0->tiktoken->openai-whisper==20250625) (2025.8.3)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch->openai-whisper==20250625) (3.19.1)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch->openai-whisper==20250625) (4.15.0)\n", "Requirement already satisfied: sympy>=1.13.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch->openai-whisper==20250625) (1.14.0)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch->openai-whisper==20250625) (3.5)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch->openai-whisper==20250625) (3.1.6)\n", "Requirement already satisfied: fsspec in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch->openai-whisper==20250625) (2025.9.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from sympy>=1.13.3->torch->openai-whisper==20250625) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from jinja2->torch->openai-whisper==20250625) (3.0.2)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages (from tqdm->openai-whisper==20250625) (0.4.6)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution ~andas (c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages)\n", "  Running command git clone --filter=blob:none --quiet https://github.com/openai/whisper.git 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-req-build-xzdykgmc'\n", "WARNING: Ignoring invalid distribution ~andas (c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~andas (c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~andas (c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~andas (c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~andas (c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n", "Requirement already satisfied: torch in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (2.8.0)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch) (3.19.1)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch) (4.15.0)\n", "Requirement already satisfied: sympy>=1.13.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch) (1.14.0)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch) (3.5)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch) (3.1.6)\n", "Requirement already satisfied: fsspec in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from torch) (2025.9.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from sympy>=1.13.3->torch) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages (from jinja2->torch) (3.0.2)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numba\\__init__.py:48: UserWarning: A NumPy version >=1.23.5 and <2.3.0 is required for this version of SciPy (detected version 2.3.2)\n", "  import scipy\n"]}], "source": ["%pip install fastapi uvicorn mutagen openai-whisper\n", "%pip install git+https://github.com/openai/whisper.git\n", "%pip install torch\n", "from fastapi import FastAPI, UploadFile, File, HTTPException, status\n", "from mutagen.mp3 import MP3\n", "from mutagen.wave import WAVE\n", "import whisper\n", "import gc\n", "import torch"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 199}, "id": "6H3jhRjCECKK", "outputId": "b0dc0737-c16c-4830-8e7f-b30722b96820"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\whisper\\transcribe.py:132: UserWarning: FP16 is not supported on CPU; using FP32 instead\n", "  warnings.warn(\"FP16 is not supported on CPU; using FP32 instead\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Confiança média: -0.5555\n", "Duração: 37.00 segundos\n", "Segmentos: 8\n", "Texto transcrito:\n"]}, {"data": {"text/plain": ["' <PERSON><PERSON><PERSON>, tudo bem? Eu só carinha, tem a gente dar com gás. Com quem eu falo? O<PERSON>, carinha, tudo bem? Aqui é a <PERSON>, eu vi quei para fazer uma reclamação de quem está lá, um gás ou um tem em casa e desde onde o sentão cheiro muito esquisito. O<PERSON>, Tai<PERSON>, então, muito importante que você abre janelas e a gente vai abanhar em que há uma tindente para o sacada. Você pode me dizer o seu SEP, é 0,05, 0,15, 0,15, 0,15. <PERSON><PERSON>, tá aí na amulha, muito obrigada. Em breve, nós já vamos chegar. E <PERSON> isso, você pode avaliar o meu atendimento, como eu não tova cederir para o atendimento de 0,10. <PERSON>u daria 10. <PERSON><PERSON> obrigada.'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["model = whisper.load_model('tiny')\n", "result = model.transcribe('comgas.mp3')\n", "\n", "confianca_media = sum(seg['avg_logprob'] for seg in result['segments']) / len(result['segments'])\n", "duracao_total = result['segments'][-1]['end'] if result['segments'] else 0\n", "\n", "print(f\"\\nConfiança média: {confianca_media:.4f}\")\n", "print(f\"Duração: {duracao_total:.2f} segundos\")\n", "print(f\"Segmentos: {len(result['segments'])}\")\n", "\n", "print(\"Texto transcrito:\")\n", "result['text']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 217}, "id": "Uxi9aMdRHYuv", "outputId": "07b9b753-c023-4e25-bca1-7b378859def6"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████| 139M/139M [00:03<00:00, 40.5MiB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Confiança média: -0.4740\n", "Duração: 37.40 segundos\n", "Segmentos: 9\n", "Texto transcrito:\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["' <PERSON><PERSON><PERSON>, tudo bem? Eu sou a Car<PERSON>, tenho indente da Alcongares, com quem eu falo. <PERSON><PERSON>, tudo bem? A<PERSON> é a <PERSON>, eu vi que aí para fazer uma reclamação de quem instalaram um gás ontem em casa e deixam-te então cheiro muito esquisido. O<PERSON>, então é muito importante que você abre janelas e a gente já vai arranhe em que há uma atendente para essa casa. Você pode me dizer o seu CP? É 0,5, 0,5, 10, 0, 20. <PERSON><PERSON>, <PERSON><PERSON>, muito obrigada. Em breve nós já vamos chegar. E é isso. Você pode avaliar o meu atendimento. Qual nota você daria para o atendimento de 0 a 10? Eu daria 10. <PERSON><PERSON> obrigada.'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import gc\n", "import torch\n", "\n", "try:\n", "    del model\n", "    gc.collect()\n", "    torch.cuda.empty_cache()\n", "except:\n", "    pass\n", "# Esse trecho garante que a memória esteja limpa e possa rodar o modelo, não retire\n", "\n", "model = whisper.load_model('base')\n", "result = model.transcribe('comgas.mp3')\n", "\n", "confianca_media = sum(seg['avg_logprob'] for seg in result['segments']) / len(result['segments'])\n", "duracao_total = result['segments'][-1]['end'] if result['segments'] else 0\n", "\n", "print(f\"\\nConfiança média: {confianca_media:.4f}\")\n", "print(f\"Duração: {duracao_total:.2f} segundos\")\n", "print(f\"Segmentos: {len(result['segments'])}\")\n", "\n", "print(\"Texto transcrito:\")\n", "result['text']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 217}, "id": "WxsFwApnnpDQ", "outputId": "a5cfa827-4546-40f9-9882-fc1f09fcc007"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████| 461M/461M [00:08<00:00, 54.4MiB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Texto transcrito:\n", "\n", "Confiança média: -0.3476\n", "Duração: 37.30 segundos\n", "Segmentos: 10\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["' <PERSON><PERSON><PERSON>, tudo bem? <PERSON>, atenden<PERSON> <PERSON> Con<PERSON>z, com quem eu falo? <PERSON><PERSON>, <PERSON><PERSON>, tudo bem? A<PERSON> <PERSON> a <PERSON>, eu liguei pra fazer uma reclamação de quem instalaram um gás ontem em casa e deixaram tentar um cheiro muito esquisito. <PERSON><PERSON>, <PERSON><PERSON>, então é muito importante que você abra as janelas e a gente vai enviar uma atendente para essa casa. Você pode me dizer o seu SEP? 05, 0510, 020. Bo<PERSON>, Thainá, muito obrigada. Em breve nós já vamos chegar. E é isso, você pode avaliar o meu atendimento? Com a nota, você daria o privatendimento de 0 a 10. Eu daria 10. <PERSON><PERSON> obrigada.'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["try:\n", "    del model\n", "    gc.collect()\n", "    torch.cuda.empty_cache()\n", "except:\n", "    pass\n", "\n", "model = whisper.load_model('small')\n", "result = model.transcribe('comgas.mp3')\n", "\n", "confianca_media = sum(seg['avg_logprob'] for seg in result['segments']) / len(result['segments'])\n", "duracao_total = result['segments'][-1]['end'] if result['segments'] else 0\n", "\n", "print(\"Texto transcrito:\")\n", "print(f\"\\nConfiança média: {confianca_media:.4f}\")\n", "print(f\"Duração: {duracao_total:.2f} segundos\")\n", "print(f\"Segmentos: {len(result['segments'])}\")\n", "\n", "result['text']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 217}, "id": "ARKHfXFGHq9W", "outputId": "79f15bdb-6c54-4593-d066-1b0363bd1b36"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████| 1.42G/1.42G [00:27<00:00, 56.1MiB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Confiança média: -0.1923\n", "Duração: 37.00 segundos\n", "Segmentos: 6\n", "Texto transcrito:\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["' <PERSON><PERSON><PERSON>, tudo bem? Eu sou a <PERSON>, atendente da Alcongás. Com quem eu falo? <PERSON><PERSON>, tudo bem? Aqui é a Tainá. Eu liguei pra fazer uma reclamação de que instalaram um gás ontem em casa e deixaram então um cheiro muito esquisito. O<PERSON>. Então é muito importante que você abre as janelas e a gente já vai enviar um atendente para a sua casa. Você pode me dizer o seu CEP? É 05-0510-021. <PERSON><PERSON>, muito obrigada. Em breve nós já vamos chegar. E é isso, você pode avaliar o meu atendimento. Qual nota você daria para o meu atendimento de 0 a 10? Eu daria 10. <PERSON><PERSON> obrigada.'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["try:\n", "    del model\n", "    gc.collect()\n", "    torch.cuda.empty_cache()\n", "except:\n", "    pass\n", "\n", "model = whisper.load_model('medium')\n", "result = model.transcribe('comgas.mp3')\n", "\n", "confianca_media = sum(seg['avg_logprob'] for seg in result['segments']) / len(result['segments'])\n", "duracao_total = result['segments'][-1]['end'] if result['segments'] else 0\n", "\n", "\n", "print(f\"\\nConfiança média: {confianca_media:.4f}\")\n", "print(f\"Duração: {duracao_total:.2f} segundos\")\n", "print(f\"Segmentos: {len(result['segments'])}\")\n", "\n", "print(\"Texto transcrito:\")\n", "result['text']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 217}, "id": "NM_T_ZN7Kg38", "outputId": "f9e4dd71-5500-44aa-9d29-482fce69afbb"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████| 2.88G/2.88G [01:02<00:00, 49.3MiB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Texto transcrito:\n", "\n", "Confiança média: -0.1298\n", "Duração: 37.04 segundos\n", "Segmentos: 6\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["' <PERSON><PERSON><PERSON>, tudo bem? Eu sou a <PERSON>, atendente da ComGaz. Com quem eu falo? <PERSON><PERSON>, <PERSON><PERSON>, tudo bem? Aqui é a Tainá. Eu liguei para fazer uma reclamação de que instalaram um gás ontem aqui em casa e deixou um cheiro muito esquisito. <PERSON><PERSON>, <PERSON><PERSON>. Então, é muito importante que você abra as janelas e a gente já vai enviar um atendente para a sua casa. Você pode me dizer o seu CEP? É... 05-0510-021. <PERSON><PERSON>, Tainá. <PERSON>ito obrigada. Em breve nós já vamos chegar. E é isso. Você pode avaliar o meu atendimento? Qual nota você daria para o meu atendimento de 0 a 10? Eu daria 10. <PERSON>ito obrigada.'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["try:\n", "    del model\n", "    gc.collect()\n", "    torch.cuda.empty_cache()\n", "except:\n", "    pass\n", "\n", "model = whisper.load_model('large')\n", "result = model.transcribe('comgas.mp3')\n", "\n", "confianca_media = sum(seg['avg_logprob'] for seg in result['segments']) / len(result['segments'])\n", "duracao_total = result['segments'][-1]['end'] if result['segments'] else 0\n", "\n", "print(\"Texto transcrito:\")\n", "print(f\"\\nConfiança média: {confianca_media:.4f}\")\n", "print(f\"Duração: {duracao_total:.2f} segundos\")\n", "print(f\"Segmentos: {len(result['segments'])}\")\n", "\n", "result['text']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 217}, "id": "_QZK1CcuDATp", "outputId": "6cdaa164-183c-47a5-f836-642e3c3b4f08"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████| 1.51G/1.51G [00:29<00:00, 54.0MiB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Texto transcrito:\n", "\n", "Confiança média: -0.1312\n", "Duração: 37.04 segundos\n", "Segmentos: 9\n"]}, {"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["' <PERSON><PERSON><PERSON>, tudo bem? Eu sou a <PERSON>, atendente da ComGás. Com quem eu falo? <PERSON><PERSON>, <PERSON><PERSON>, tudo bem? Aqui é a Tainá. Eu liguei para fazer uma reclamação de que instalaram o Gás ontem aqui em casa e deixei ontem estar um cheiro muito esquisito. <PERSON><PERSON>, <PERSON><PERSON>. Então, é muito importante que você abra as janelas e a gente já vai enviar um atendente para a sua casa. Você pode me dizer o seu CEP? É 05, 0510, 021. <PERSON><PERSON>, Tai<PERSON>. <PERSON>ito obrigada. Em breve nós já vamos chegar. E é isso. Você pode avaliar o meu atendimento? Qual nota você daria para o meu atendimento de 0 a 10? Eu daria 10. <PERSON><PERSON> obrigada.'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["try:\n", "    del model\n", "    gc.collect()\n", "    torch.cuda.empty_cache()\n", "except:\n", "    pass\n", "\n", "model = whisper.load_model('turbo')\n", "result = model.transcribe('comgas.mp3')\n", "\n", "confianca_media = sum(seg['avg_logprob'] for seg in result['segments']) / len(result['segments'])\n", "duracao_total = result['segments'][-1]['end'] if result['segments'] else 0\n", "\n", "print(\"Texto transcrito:\")\n", "print(f\"\\nConfiança média: {confianca_media:.4f}\")\n", "print(f\"Duração: {duracao_total:.2f} segundos\")\n", "print(f\"Segmentos: {len(result['segments'])}\")\n", "\n", "result['text']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import uvicorn\n", "import nest_asyncio\n", "\n", "# Permite rodar o servidor no notebook\n", "nest_asyncio.apply()\n", "\n", "# Roda a API FastAPI\n", "uvicorn.run(\"main:app\", host=\"0.0.0.0\", port=8000, reload=False)"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 0}