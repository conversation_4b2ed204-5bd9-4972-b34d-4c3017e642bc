from __future__ import annotations
from fastapi import APIRouter, UploadFile, File, status, HTTPException, BackgroundTasks, Request
from mutagen.mp3 import MP3
from mutagen.wave import WAVE
import tempfile, os, json, time, uuid, logging

import whisper

from app.config import ALLOWED_AUDIO_TYPES as allowed_types, TRANSCRICOES_DIR
from app.api.common.metrics import (
    TRANSCRIPTIONS_IN_FLIGHT, TRANSCRIPTIONS_TOTAL, TRANSCRIPTION_DURATION_SECONDS,
    TRANSCRIPTIONS_TOTAL_BY_TYPE, TRANSCRIPTION_SEGMENTS
)
from app.api.common.utils import (
    ensure_dir, next_transcription_name, trigger_eval_webhook, pipeline_mark_start
)

logger = logging.getLogger("app")

# Carrega Whisper uma vez neste módulo
WHISPER_MODEL = whisper.load_model("turbo")

router = APIRouter(prefix="/stt", tags=["stt"])

@router.post(
    "/transcribe",
    responses={
        200: {"description": "Transcrição realizada, salva em .json e webhook disparado"},
        400: {"description": "Erro de validação do arquivo"},
        415: {"description": "Tipo de mídia não suportado"},
        500: {"description": "Erro interno ao processar o áudio"}
    }
)
async def transcribe_audio(
    background_tasks: BackgroundTasks,
    request: Request,
    audio_file: UploadFile = File(...)
):
    t0 = time.time()
    correlation_id = request.headers.get("X-Request-Id", str(uuid.uuid4()))
    TRANSCRIPTIONS_IN_FLIGHT.inc()
    logger.info({
        "event": "transcription_started",
        "filename": getattr(audio_file, "filename", None),
        "contentType": getattr(audio_file, "content_type", None),
        "correlationId": correlation_id,
        "path": "/stt/transcribe",
        "method": "POST",
    })

    # 1) Tipo
    if audio_file.content_type not in allowed_types:
        TRANSCRIPTIONS_TOTAL.labels(status="error").inc()
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail={
                "code": "Tipo de mídia não suportado",
                "message": f"Apenas arquivos MP3 ou WAV são permitidos. Recebido: {audio_file.content_type}."
            }
        )
    # 2) Conteúdo
    raw_bytes = await audio_file.read()
    if not raw_bytes:
        TRANSCRIPTIONS_TOTAL.labels(status="error").inc()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"code": "Arquivo vazio", "message": "O arquivo de áudio está vazio."}
        )

    suffix = ".mp3" if audio_file.content_type == "audio/mpeg" else ".wav"
    tmp_path = None
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
            tmp.write(raw_bytes)
            tmp_path = tmp.name

        # Duração (segundos) se disponível
        try:
            meta = MP3(tmp_path) if audio_file.content_type == "audio/mpeg" else WAVE(tmp_path)
            duration_seconds = float(meta.info.length)
        except Exception:
            duration_seconds = None

        # 3) Transcrever
        result = WHISPER_MODEL.transcribe(tmp_path)
        segments = result.get("segments", []) or []
        if segments:
            confianca_media = sum(seg.get("avg_logprob", 0.0) for seg in segments) / len(segments)
            duracao_total = segments[-1].get("end", 0.0)
        else:
            confianca_media = None
            duracao_total = duration_seconds if duration_seconds is not None else 0.0

        try:
            TRANSCRIPTION_SEGMENTS.observe(float(len(segments)))
        except Exception:
            pass

        texto = (result.get("text") or "").strip()
        payload = {
            "arquivo": audio_file.filename,
            "modelo": "turbo",
            "tipo": audio_file.content_type,
            "duracao_segundos": duracao_total if duracao_total else duration_seconds,
            "confianca_media": confianca_media,
            "segmentos": len(segments),
            "texto": texto
        }

        # 4) Salvar JSON
        ensure_dir(TRANSCRICOES_DIR)
        document_name = next_transcription_name(TRANSCRICOES_DIR)
        out_path = os.path.join(TRANSCRICOES_DIR, document_name)
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(payload, f, ensure_ascii=False, indent=2)

        # 5) Disparar webhook de avaliação
        background_tasks.add_task(trigger_eval_webhook, document_name, correlation_id)

        # 6) Marcar início p/ E2E
        pipeline_mark_start(document_name, t0)

        # Métricas/logs
        dur = time.time() - t0
        TRANSCRIPTION_DURATION_SECONDS.observe(dur)
        TRANSCRIPTIONS_TOTAL.labels(status="success").inc()
        type_label = "mp3" if audio_file.content_type == "audio/mpeg" else "wav"
        TRANSCRIPTIONS_TOTAL_BY_TYPE.labels(status="success", type=type_label).inc()

        logger.info({
            "event": "transcription_saved",
            "document_name": document_name,
            "saved_at": out_path,
            "durationMs": int(dur * 1000),
            "segments": len(segments),
            "confianca_media": confianca_media,
            "correlationId": correlation_id,
        })

        return {
            "status": "ok",
            "document_name": document_name,
            "saved_at": out_path
        }

    except HTTPException:
        raise
    except Exception as e:
        TRANSCRIPTIONS_TOTAL.labels(status="error").inc()
        try:
            type_label = "mp3" if getattr(audio_file, "content_type", "") == "audio/mpeg" else "wav"
            TRANSCRIPTIONS_TOTAL_BY_TYPE.labels(status="error", type=type_label).inc()
        except Exception:
            pass
        logger.error({
            "event": "transcription_error",
            "error": str(e),
            "correlationId": correlation_id,
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"code": "Erro interno", "message": f"Falha ao transcrever: {str(e)}"}
        )
    finally:
        try: TRANSCRIPTIONS_IN_FLIGHT.dec()
        except Exception: pass
        if tmp_path and os.path.exists(tmp_path):
            try: os.remove(tmp_path)
            except Exception: pass
