from __future__ import annotations
import os, re, json, time, threading, logging
from typing import Dict, Any, Optional
import httpx

from app.config import TRANSCRICOES_DIR, EVAL_WEBHOOK_URL

logger = logging.getLogger("app")

# Controle de início do pipeline p/ calcular E2E no final
_pipeline_start_ts: Dict[str, float] = {}
_pipeline_lock = threading.Lock()

def ensure_dir(path: str) -> None:
    os.makedirs(path, exist_ok=True)

def next_transcription_name(dirpath: str) -> str:
    """
    Gera próximo nome sequencial: transcricao_001.json, transcricao_002.json, ...
    """
    ensure_dir(dirpath)
    padrao = re.compile(r"^transcricao_(\d{3})\.json$")
    indices = []
    for fn in os.listdir(dirpath):
        m = padrao.match(fn)
        if m:
            indices.append(int(m.group(1)))
    prox = (max(indices) + 1) if indices else 1
    return f"transcricao_{prox:03d}.json"

async def trigger_eval_webhook(document_name: str, correlation_id: Optional[str] = None) -> None:
    headers = {}
    if correlation_id:
        headers["X-Request-Id"] = correlation_id
    try:
        t0 = time.time()
        async with httpx.AsyncClient(timeout=30) as client:
            await client.post(EVAL_WEBHOOK_URL, json={"document_name": document_name}, headers=headers)
        from .metrics import WEBHOOK_LATENCY
        WEBHOOK_LATENCY.observe(time.time() - t0)
        logger.info({
            "event": "webhook_enqueued",
            "document_name": document_name,
            "webhook": EVAL_WEBHOOK_URL,
            "correlationId": correlation_id,
        })
    except Exception as exc:
        from .metrics import WEBHOOK_FAILURES_TOTAL
        WEBHOOK_FAILURES_TOTAL.inc()
        logger.error({
            "event": "webhook_error",
            "document_name": document_name,
            "error": str(exc),
            "webhook": EVAL_WEBHOOK_URL,
            "correlationId": correlation_id,
        })

def pipeline_mark_start(document_name: str, ts: Optional[float] = None) -> None:
    with _pipeline_lock:
        _pipeline_start_ts[document_name] = ts if ts is not None else time.time()

def pipeline_pop_start(document_name: str) -> Optional[float]:
    with _pipeline_lock:
        return _pipeline_start_ts.pop(document_name, None)
