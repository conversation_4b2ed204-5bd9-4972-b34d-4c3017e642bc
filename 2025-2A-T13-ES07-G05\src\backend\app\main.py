from fastapi import Fast<PERSON><PERSON>
from app.observability import setup_observability
from fastapi.middleware.cors import CORSMiddleware
from app.health import router as health_router

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8080", "http://127.0.0.1:8080"], 
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

from app.api.routers import router
app.include_router(router)

# Inicializa observabilidade (logs JSON, /metrics) de forma segura.
setup_observability(app)

# Rota de healthcheck simples com ping opcional ao Mongo.
app.include_router(health_router)


@app.get("/")
async def root():
    return {"message": "Hello World"}
