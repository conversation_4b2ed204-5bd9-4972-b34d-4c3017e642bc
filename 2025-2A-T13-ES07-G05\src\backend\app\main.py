# src/backend/app/main.py
from fastapi import FastAPI
from app.observability import setup_observability
from fastapi.middleware.cors import CORSMiddleware
from app.health import router as health_router

# NEW: importa routers por domínio
from app.api.upload.router import router as upload_router
from app.api.stt.router import router as stt_router
from app.api.nlp.router import router as nlp_router

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8080", "http://127.0.0.1:8080"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Registra routers separados
app.include_router(upload_router)
app.include_router(stt_router)
app.include_router(nlp_router)

# Observabilidade e health
setup_observability(app)
app.include_router(health_router)

@app.get("/")
async def root():
    return {"message": "Hello World"}
