<Table>
  <tr>
    <td><a href= "https://www.comgas.com.br/"><img src="img/comgas.png" alt="Comgas" border="0"></td>
    <td>
      <a href= "https://www.inteli.edu.br/"><img src="img/logo-Inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
    </td>
  </tr>
</table>

# Nome do Projeto: Carbon

## Nome do Grupo: Carbon

## Integrantes:

- <a href="https://www.linkedin.com/in/anacdejesus//">Ana Carolina de Jesus Pac<PERSON> da <PERSON></a>
- <a href="https://www.linkedin.com/in/felipe-zillo-72b367247/"><PERSON></a>
- <a href="https://www.linkedin.com/in/fernandobertholdo/"><PERSON></a>
- <a href="https://www.linkedin.com/in/henrique-botti-6272571a0/"><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/karine-victoria//">Karine Victoria Rosa da Paixão</a>
- <a href="https://www.linkedin.com/in/natalycunha /">Nataly de Souza Cunha</a>
- <a href="https://www.linkedin.com/in/tainacortez/">Tainá de Paiva Cortez</a>

# Sumário
- [1. Introdução](#1-introdução)
- [2. Avaliação das Sprints](#2-avaliação-das-sprints)
  - [2.1 Sprint 1](#21-sprint-1)
  - [2.2 Sprint 2](#22-sprint-2)
  - [2.3 Sprint 3](#23-sprint-3)
  - [2.4 Sprint 4](#24-sprint-4)
  - [2.5 Sprint 5](#25-sprint-5)
- [3. Análise de Riscos](#3-análise-de-riscos)
  - [3.1 Riscos identificados](#31-riscos-identificados)
  - [3.2 Mitigação de Riscos](#32-mitigação-de-riscos)
- [4. Análise de Métricas: Evolução do Módulo](#4-análise-de-métricas-evolução-do-módulo)
  - [4.1 Evolução das Métricas de Percepção (Sprints 1 a 5)](#41-evolução-das-métricas-de-percepção-sprints-1-a-5)
  - [4.2 Evolução das Métricas de Gestão de Projeto (Sprints 1 a 5)](#42-evolução-das-métricas-de-gestão-de-projeto-sprints-1-a-5)
- [5. Análise Post Mortem](#5-análise-post-mortem)
  - [5.1 Sucessos do Projeto](#51-sucessos-do-projeto)
  - [5.2 Oportunidades de Melhoria](#52-oportunidades-de-melhoria)
  - [5.3 Lições Aprendidas](#53-lições-aprendidas)

# 1. Introdução
&emsp; O objetivo deste documento é registrar de maneira estruturada a evolução do projeto desenvolvido pelo grupo ao longo das sprints. Através da análise crítica dos pontos fortes e dos pontos a serem melhorados identificados durante o ciclo de desenvolvimento, este documento visa não apenas acompanhar o desempenho do time, mas também orientar a implementação de ações de melhoria contínua. O registro sistemático dessas avaliações tem como propósito fortalecer a colaboração, a eficiência e a qualidade das entregas do grupo.

&emsp; Além da análise contínua de desempenho nas sprints, este documento também contempla a identificação de riscos que poderiam impactar o projeto, as estratégias de mitigação propostas para cada risco mapeado e, por fim, uma análise *post mortem* que reflete sobre os sucessos alcançados, as oportunidades de melhoria e as lições aprendidas ao longo do processo.

# 2. Avaliação das Sprints
&emsp; Nesta seção, são apresentadas as análises realizadas ao final de cada sprint, baseadas nas *Sprint Reviews* e *Retrospectives*. Para cada sprint, serão mensurados alguns fatores, de acordo com o consenso geral do grupo. Além disso, haverá uma pequena tabela com informações sobre a gestão do projeto, que será preenchida pelo Scrum Master. Essa abordagem permite identificar padrões de comportamento, aprendizados e oportunidades de evolução, proporcionando ao time uma base sólida para aprimorar suas práticas e maximizar a eficácia nas próximas sprints do projeto.

## 2.1 Sprint 1
### Percepção da Equipe *(Notas de 0 a 10)*  

<div align="center">

| Percepções da equipe          | Nota (0–10) |  
|:-----------------------------:|:-----------:|  
| **Comunicação**               |      10     |  
| **Gestão de Cards**           |      7      |  
| **Correção de PR**            |      6      |  
| **Engajamento**               |      9      |  
| **Qualidade das entregas**    |      10     |  
| **Aprendizado**               |      8      |  

</div>

&emsp; De forma geral, a equipe manteve uma comunicação alinhada, o que contribuiu para entregas de qualidade. No entanto, foram identificados desafios na gestão de cards e nas revisões de PRs. Esses pontos impactaram a fluidez do sprint.

### Gestão do Projeto *(Resposta em 1 palavra)*  

<div align="center">

| Gestão do Projeto                    | Resposta |  
|-----------------------------|----------|  
| **Atrasos no projeto?**     |     Não     |  
| **Retrabalhos no projeto?** |     Não     |  
| **Escopo das tasks bem fragmentado?**|     Sim     |  
| **Projeto entregue na data correta?**|     Sim     |  

</div>

## 2.2 Sprint 2
### Percepção da Equipe *(Notas de 0 a 10)*  

<div align="center">

| Percepções da equipe          | Nota (0–10) |  
|:-----------------------------:|:-----------:|  
| **Comunicação**               |      8     |  
| **Gestão de Cards**           |      8      |  
| **Correção de PR**            |      7      |  
| **Engajamento**               |      9      |  
| **Qualidade das entregas**    |      9     |  
| **Aprendizado**               |      9      |  

</div>

&emsp; A equipe percebeu uma melhora no uso do Trello, com tarefas mais bem detalhadas, embora ainda haja espaço para avanços na gestão dos cards diários. Por outro lado, a comunicação não foi tão satisfatória quanto poderia ser, já que alguns membros relataram não ter uma visão clara sobre o que os demais estavam fazendo. A correção das PRs também apresentou dificuldades, pois muitas foram revisadas apenas no dia anterior ao encerramento da sprint. Além disso, a preparação da apresentação acabou sendo realizada em cima da hora.

**Plano de ação para melhorias**

- **Uso do Trello:** Manter o detalhamento das tarefas e fazer uma revisão rápida no início da semana para garantir clareza e boa gestão.
- **Comunicação:** Realizar reuniões curtas (20 a 30 min) durante a semana para que todos saibam o andamento das atividades.
- **Correção de PRs:** Definir prazos intermediários (toda quarta-feira, todos os PRs precisam estar revisados) para evitar acúmulo no final da sprint.
- **Apresentação:** reservar um momento fixo pelo menos um dia antes da entrega para preparar e revisar em conjunto.

### Gestão do Projeto *(Resposta em 1 palavra)*  

<div align="center">

| Gestão do Projeto                    | Resposta |  
|-----------------------------|----------|  
| **Atrasos no projeto?**     |     Não     |  
| **Retrabalhos no projeto?** |     Não     |  
| **Escopo das tasks bem fragmentado?**|     Sim     |  
| **Projeto entregue na data correta?**|     Sim     |  

</div>

## 2.3 Sprint 3
<div align="center">

| Percepções da equipe          | Nota (0–10) |  
|:-----------------------------:|:-----------:|  
| **Comunicação**               |      8     |   
| **Gestão de Cards**           |      6      |   
| **Correção de PR**            |      10      |   
| **Engajamento**               |      8      |   
| **Qualidade das entregas**    |      9     |   
| **Aprendizado**               |      10      |   

</div>

&emsp; A equipe manteve uma boa comunicação, garantindo que todos estivessem alinhados sobre o andamento das tarefas. Contudo, houve uma queda na gestão dos cards, que ficaram menos organizados e dificultaram o acompanhamento diário das atividades. Em contrapartida, a correção de PRs foi um ponto de grande destaque, pois todas as revisões ocorreram de forma antecipada e sem acúmulos. O engajamento permaneceu alto, com participação ativa dos membros, e a qualidade das entregas continuou consistente. O aprendizado também se destacou, com todos relatando evolução significativa nas competências técnicas e de colaboração, principalmente por ser uma sprint mais voltada a partes mais técnicas em si.

**Plano de ação para melhorias**

- **Gestão de Cards:** Reforçar a organização das tarefas no Trello, garantindo revisões frequentes e clareza na atribuição de responsáveis.  
- **Comunicação:** Manter as reuniões curtas semanais para alinhamento, garantindo que a visão do projeto seja compartilhada entre todos.  
- **Correção de PRs:** Continuar com os prazos intermediários de revisão, consolidando a prática adotada nesta sprint.  
- **Engajamento e aprendizado:** Incentivar a troca de conhecimentos entre os membros, promovendo pequenos momentos de compartilhamento técnico.  

### Gestão do Projeto *(Resposta em 1 palavra)*  

<div align="center">

| Gestão do Projeto                    | Resposta |  
|-----------------------------|----------|  
| **Atrasos no projeto?**     |     Não     |  
| **Retrabalhos no projeto?** |     Não     |  
| **Escopo das tasks bem fragmentado?**|     Sim     |  
| **Projeto entregue na data correta?**|     Sim     |  

</div>

## 2.4 Sprint 4

| Percepção/Aspecto | Nota (0–10) / Resposta | Tipo |
|:-----------------------------:|:----------------------:|:----------:|
| **Comunicação** | 7 | Percepção da Equipe |
| **Gestão de Cards** | 6 | Percepção da Equipe |
| **Correção de PR** | 8 | Percepção da Equipe |
| **Engajamento** | 7 | Percepção da Equipe |
| **Qualidade das entregas** | 8 | Percepção da Equipe |
| **Aprendizado** | 10 | Percepção da Equipe |
| **Atrasos no projeto?** | Não | Gestão do Projeto |
| **Retrabalhos no projeto?** | Não | Gestão do Projeto |
| **Escopo das tasks bem fragmentado?**| Sim | Gestão do Projeto |
| **Projeto entregue na data correta?**| Sim | Gestão do Projeto |

&emsp; A equipe registrou uma queda nas notas de Comunicação e Engajamento, indicando que o alinhamento e a participação geral sofreram leves declínios durante a sprint. O ponto de maior atenção, a Gestão de Cards, manteve a nota mais baixa, refletindo dificuldades na organização e acompanhamento das tarefas. A Correção de PR também apresentou uma queda, dado que o fluxo de revisões não foi tão antecipado quanto o desejado. Em contrapartida, a Qualidade das Entregas permaneceu em um nível satisfatório, e o Aprendizado se destacou com nota máxima, provando que o foco no desenvolvimento técnico e colaborativo foi um sucesso. As métricas de gestão do projeto continuam sólidas, sem atrasos, retrabalhos, e com escopo bem fragmentado, garantindo a entrega no prazo.

**Plano de ação para melhorias**

- **Gestão de Cards:** Reforçar a organização das tarefas no quadro, introduzindo uma revisão rápida diária na daily para garantir clareza e responsabilidade pelos cards em andamento.
- **Comunicação e Engajamento:** Incentivar ativamente o *pair programming* para tarefas complexas, visando melhorar o alinhamento e aumentar o envolvimento dos membros simultaneamente.
- **Correção de PRs:** Designar um bloco de tempo fixo na agenda dos membros para a correção de PRs, consolidando a prática de revisão antecipada.


## 2.5 Sprint 5


| Percepção/Aspecto | Nota (0–10) / Resposta | Tipo |
|:-----------------------------:|:----------------------:|:----------:|
| **Comunicação** | 9 | Percepção da Equipe |
| **Gestão de Cards** | 7 | Percepção da Equipe |
| **Correção de PR** | 9 | Percepção da Equipe |
| **Engajamento** | 9 | Percepção da Equipe |
| **Qualidade das entregas** | 10 | Percepção da Equipe |
| **Aprendizado** | 8 | Percepção da Equipe |
| **Atrasos no projeto?** | Não | Gestão do Projeto |
| **Retrabalhos no projeto?** | Sim | Gestão do Projeto |
| **Escopo das tasks bem fragmentado?**| Sim | Gestão do Projeto |
| **Projeto entregue na data correta?**| Sim | Gestão do Projeto |

---

&emsp; A **Sprint 5** marcou uma melhora clara em áreas importantes depois da **retrospectiva** detalhada feita no início do sprint, que incluiu feedbacks individuais e bastante transparência. A Comunicação e o Engajamento ficaram com notas melhores, mostrando que o comprometimento definido na *planning* foi levado a sério.

&emsp; A Planning foi um ponto muito positivo da sprint. Ela foi bem organizada (usando o **Excalidraw**), o que ajudou a dividir as tarefas em partes menores e deixou as responsabilidades claras. Essa boa preparação inicial foi importante para manter a produção, principalmente na primeira semana, que teve a prova do módulo, e permitiu que a equipe entregasse tudo funcionando no prazo.

&emsp; No entanto, a **Gestão de Cards** ainda é uma área que **precisa de mais atenção**, mesmo tendo melhorado um pouco em relação à Sprint 4. A organização inicial foi ótima, mas o acompanhamento diário e a manutenção do quadro poderiam ser melhoradas. Além disso, na sprint 5 houve Retrabalho com itens da Sprint 4, confirmando que a refatoração precisou ser feita e gastou uma parte do tempo da equipe.

&emsp; De modo geral, a sprint foi bem-sucedida nas entregas e no envolvimento da equipe, mostrando que o time consegue lidar com desafios e que uma *planning* bem feita faz diferença. A Qualidade das Entregas continuou boa, e o prazo foi mantido, resultando em uma boa entrega final.

---

**Plano de ação para melhorias**

- **Gestão de Cards:**
    - **Ação:** Criar um momento de 5 minutos antes da reunião diária (Daily Meeting), focado apenas em **mover e atualizar** os cards no quadro. Isso garante que o status das tarefas fique claro para todos.
    

- **Retrabalho e Refatoração:**
    - **Ação:** Usar uma etiqueta (tag ou label) especial como **"Priorizar Refatoração"** para as tarefas que são dívidas técnicas das sprints anteriores. Assim, é possível medir quanto tempo o retrabalho consome e dar prioridade a ele.
    

- **Comunicação e Feedback:**
    - **Ação:** Manter o **Momento Feedback** (o encontro de retrospectiva e feedback individual) em intervalos regulares (como a cada duas sprints), já que ele ajudou a melhorar o **Engajamento** e a **Comunicação** do time nesta sprint.
    


# 3. Análise de Riscos


Esta seção avalia os principais problemas e incertezas que foram identificados no início do projeto e que surgiram ao longo das sprints. Também é feito um resumo de como a equipe lidou com esses pontos até o final do módulo.

## 3.1 Riscos identificados


Os riscos que realmente afetaram o projeto não foram relacionados a atrasos na data final de entrega ou falhas técnicas graves, mas sim a problemas na organização interna, na comunicação e em eventos externos.

| Risco Identificado | Ocorrência no Projeto | Impacto |
|:---:|:---:|:---:|
| **Comunicação Desalinhada** | Sim (Sprints 2 e 4) | Diminuição da clareza sobre o trabalho dos colegas e necessidade de maior alinhamento. |
| **Baixa Gestão de Cards** | Sim (Sprints 1, 3, 4 e 5) | Dificuldade em acompanhar o progresso diário das tarefas. A equipe se organizava mais pelo Excalidraw e acabava esquecendo o Trello. |
| **Acúmulo de Revisões de PRs** | Sim (Sprints 1, 2 e 4) | Atraso nas correções e risco de realizar revisões apressadas no final da sprint. |
| **Retrabalho e Débito Técnico** | Sim (Sprint 5) | Consumo de tempo do ciclo atual para corrigir ou refatorar tarefas de sprints anteriores. |
| **Baixo Engajamento** | Sim (Sprint 4) | Diminuição da participação ativa de alguns membros nas discussões e tarefas. |
| **Falta de Tempo/Foco (Eventos Externos)** | Sim (Sprint 5) | Perda de tempo dedicado ao projeto devido a semana da prova e participação em eventos como a Feira de Carreira da USP e a Feira de Carreira do Inteli, além da participação em processos seletivos durante módulo |

---

## 3.2 Mitigação de Riscos


O acompanhamento e o fechamento do módulo mostram que a equipe conseguiu agir em cima dos riscos identificados. As principais estratégias de mitigação aplicadas foram:

* **Comunicação:** O risco de desalinhamento foi combatido com a implementação de reuniões curtas e semanais e o reforço da transparência por meio das retrospectivas.

* **Gestão de Cards:** Este foi um risco persistente. A mitigação envolveu tentativas de melhorar o detalhamento das tarefas, mas o problema era que a equipe se concentrava mais na organização visual pelo **Excalidraw** e não mantinha o **Trello** atualizado. O plano de ação final foca em **revisões rápidas diárias** para resolver isso.

* **Acúmulo de Revisões (PRs):** O risco foi contornado com sucesso a partir da definição de prazos intermediários fixos. Um ponto de virada foi a definição de revisores fixos na retrospectiva da Sprint 3, o que ajudou bastante a garantir que as revisões fossem feitas a tempo e com qualidade.

* **Retrabalho:** O impacto do retrabalho foi minimizado pela boa qualidade do planejamento na Sprint 5, que permitiu que o time alocasse tempo para a refatoração sem comprometer a entrega.

* **Engajamento:** A queda de engajamento ocorreu na sprint 4, com a retrospectiva e planning que aconteceram na sprint 5 foi possível recuperar aquilo que não foi feito na sprint 4.

* **Falta de Tempo/Foco (Eventos Externos):** O risco relacionado a **provas e feiras de carreira** foi mitigado por um **planejamento mais cuidadoso e realista** (na Sprint 5), que levou em conta o tempo disponível da equipe, garantindo que a entrega fosse completa mesmo com menos horas dedicadas em certos períodos.

---

No geral, o grande aprendizado do módulo foi que os riscos internos (processo e comunicação) foram os mais relevantes, e a equipe desenvolveu planos de ação para lidar com eles, garantindo que o objetivo final de entrega no prazo e com qualidade fosse atingido em todas as sprints.





# 4. Análise de Métricas: Evolução do Módulo

Esta seção avalia o desempenho da equipe ao longo das cinco sprints, analisando como as notas de percepção e as métricas de gestão do projeto mudaram. O objetivo é entender a evolução do comportamento da equipe e identificar tendências de melhoria.

## 4.1 Evolução das Métricas de Percepção (Sprints 1 a 5)

A tabela a seguir consolida as notas (de 0 a 10) dadas pela equipe em cada sprint:

| Métrica | Sprint 1 | Sprint 2 | Sprint 3 | Sprint 4 | Sprint 5 |
|:---:|:---:|:---:|:---:|:---:|:---:|
| **Comunicação** | 10 | 8 | 8 | 7 | **9** |
| **Gestão de Cards** | 7 | 8 | 6 | 6 | **7** |
| **Correção de PR** | 6 | 7 | 10 | 8 | **9** |
| **Engajamento** | 9 | 9 | 8 | 7 | **9** |
| **Qualidade das Entregas** | 10 | 9 | 9 | 8 | **10** |
| **Aprendizado** | 8 | 9 | 10 | 10 | **8** |

### Conclusões sobre as Tendências

**1. Comunicação e Engajamento: Forte Recuperação Pós-Feedback**
* **Tendência:** Ambas as métricas caíram nas Sprints 3 e 4, mas tiveram uma **recuperação clara na Sprint 5** (subindo de 7 para **9**).
* **Conclusão (Eficácia):** A **ação de mitigação** (retrospectiva com *feedback* individual) funcionou bem. A equipe mostrou que, quando identifica o problema de interação, consegue se realinhar rapidamente, voltando a um alto nível de colaboração.

**2. Qualidade das Entregas: Padrão Alto e Fechamento com Nota Máxima**
* **Tendência:** A métrica começou em 10, teve uma queda na Sprint 4 (8), mas retornou à nota máxima (10) na Sprint 5.
* **Conclusão (Eficácia):** Apesar do retrabalho na Sprint 5, o time conseguiu garantir que todas as tarefas entregues ao final do módulo tivessem a **qualidade esperada**. Isso mostra que a equipe priorizou o resultado final, mesmo sob pressão.

**3. Correção de PRs: Consolidação do Processo de Revisão**
* **Tendência:** A métrica subiu de 6 (Sprint 1) para 10 (Sprint 3) e se estabilizou em um nível alto (Sprint 5: **9**).
* **Conclusão (Eficiência):** A introdução de **prazos intermediários e revisores fixos** foi uma melhoria de processo duradoura. A nota 9 na Sprint 5 confirma que o risco de acúmulo de revisões foi resolvido.

**4. Gestão de Cards: O Desafio Recorrente de Acompanhamento**
* **Tendência:** Esta métrica continua sendo a mais baixa (notas 6 e 7), com pouca variação.
* **Conclusão (Eficiência):** É o **principal ponto de ineficiência**. A equipe é eficaz no *planejamento* inicial (com o Excalidraw), mas tem dificuldade em manter a transparência e a organização diária no Trello. Melhorar isso é o passo mais importante para aumentar a eficiência futura.

**5. Aprendizado: Foco e Estabilidade**
* **Tendência:** A métrica atingiu o pico nas Sprints 3 e 4 (10), voltando para 8 na Sprint 5.
* **Conclusão:** O time demonstrou um forte **foco no desenvolvimento técnico** nas sprints intermediárias. A queda na Sprint 5 pode ser explicada pelo foco em **entrega e correção de débitos técnicos**, tirando o foco da exploração de novos aprendizados.

## 4.2 Evolução das Métricas de Gestão de Projeto (Sprints 1 a 5)

| Métrica | Sprint 1 | Sprint 2 | Sprint 3 | Sprint 4 | Sprint 5 |
|:---:|:---:|:---:|:---:|:---:|:---:|
| **Atrasos no projeto?** | Não | Não | Não | Não | Não |
| **Retrabalhos no projeto?** | Não | Não | Não | Não | **Sim** |
| **Escopo das tasks bem fragmentado?** | Sim | Sim | Sim | Sim | Sim |
| **Projeto entregue na data correta?** | Sim | Sim | Sim | Sim | Sim |

### Conclusões sobre Eficiência e Entrega

**1. Entrega no Prazo (Eficácia):**
* A equipe **entregou no prazo** em todas as cinco sprints.
* **Conclusão:** A **eficácia** do time em cumprir os compromissos é muito boa. A consistência na entrega, mesmo com problemas internos e externos, é o resultado mais positivo do projeto.

**2. Retrabalho e Qualidade (Eficiência):**
* O único "Sim" em **Retrabalhos no projeto?** foi na **Sprint 5**, coincidindo com a queda da Qualidade das Entregas na Sprint 4.
* **Conclusão:** Isso estabelece uma relação de causa e efeito: a qualidade ligeiramente menor de uma sprint gerou um **débito técnico** na sprint seguinte. O time foi eficiente em identificar e corrigir o problema, mas precisou gastar tempo com retrabalho para isso.

**3. Planejamento (Eficiência):**
* O **Escopo das tasks bem fragmentado?** foi "Sim" em todas as sprints.
* **Conclusão:** A equipe manteve uma **boa eficiência no planejamento**, o que facilitou o trabalho e ajudou a garantir o sucesso da entrega final.




# 5. Análise Post Mortem

Esta reflexão final avalia o projeto como um todo, destacando os resultados positivos da equipe e identificando os pontos em que o processo pode ser melhorado em trabalhos futuros.

## 5.1 Sucessos do Projeto

A equipe conseguiu ter um desempenho consistente ao longo do módulo, entregando um produto de alto valor e desenvolvendo práticas de trabalho que melhoraram a cada sprint.

* **Consistência na Entrega:** O time conseguiu entregar o projeto no prazo em todas as cinco sprints, demonstrando alta eficácia e capacidade de cumprir o que foi planejado, mesmo com desafios internos e externos.
* **Qualidade Técnica Elevada:** A qualidade das entregas no fechamento do módulo foi máxima, mostrando que a equipe priorizou o resultado final, mesmo tendo que lidar com retrabalho no meio do caminho.
* **Melhoria Contínua no Processo:** A equipe soube transformar pontos fracos em pontos fortes. A Correção de PRs, que era um problema inicial, foi resolvida com a adoção de revisores fixos e prazos intermediários, melhorando o fluxo de trabalho.
* **Recuperação de Engajamento:** Após um período de queda na participação e comunicação, a equipe usou a retrospectiva com feedback individual para recuperar rapidamente o Alinhamento e o Engajamento, provando a maturidade para se realinhar.
* **Arquitetura:** A solução foi construída com uma arquitetura modular clara, usando **Docker** para garantir que o projeto pudesse rodar em qualquer lugar (portabilidade) e implementou **Observabilidade (Prometheus/Grafana)** e **filas assíncronas (RabbitMQ)**, que são essenciais para um produto escalável.
* **Valor para o Cliente:** A solução entregou um valor claro, permitindo que a Comgás passasse de **1% para uma amostragem relevante de análise**, transformando o trabalho do analista de operacional para estratégico.

## 5.2 Oportunidades de Melhoria

Os principais desafios encontrados foram no acompanhamento diário das tarefas, o que se tornou o maior foco de melhoria para projetos futuros.

* **Gestão de Cards:** Este foi o **ponto mais fraco** do projeto, com notas consistentemente baixas. A equipe se organizava bem no planejamento visual com o **Excalidraw**, mas não mantinha o **Trello** atualizado, dificultando a transparência e o acompanhamento diário do progresso.
* **Organização de Ferramentas:** É preciso garantir que a ferramenta de planejamento (como o Excalidraw) e a ferramenta de acompanhamento (como o Trello) estejam sempre sincronizadas, evitando que o time se concentre em uma e se esqueça da outra.
* **Gestão de Débito Técnico:** A ocorrência de **retrabalho na Sprint 5** devido a pendências da Sprint 4 indica que a qualidade precisa ser mantida de forma consistente, e não apenas no final do projeto, para evitar que tempo seja gasto corrigindo o que deveria estar pronto.
## 5.3 Lições Aprendidas

As lições tiradas do projeto focam em como aprimorar a colaboração e a eficiência para os próximos módulos:

1.  **A Ação Vence o Plano:** Planejar bem é importante, mas a disciplina de mover os cards diariamente é o que garante a eficiência e a transparência do processo.
2.  **Qualidade é Continuidade:** O retrabalho prova que o padrão de qualidade em uma sprint afeta diretamente o tempo e o esforço da sprint seguinte. Manter a qualidade alta em todas as etapas evita sobrecarga no futuro.
3.  **Feedback Formalizado:** A prática de realizar retrospectivas com feedback individual e transparência é uma ferramenta poderosa para resolver problemas de comunicação e engajamento. Essa prática deve ser mantida.
4.  **Processos Fortes para Tarefas Repetitivas:** Definir revisores fixos para PRs foi uma solução de processo simples que trouxe um grande ganho de eficiência e deve ser replicada.
5.  **Planejamento deve ser Realista:** É preciso considerar o impacto de eventos externos (provas, feiras de carreira) e criar um planejamento realista que leve essas pausas em conta.
6.  **Observabilidade:** A experiência com **Prometheus e Grafana** mostrou que ter visibilidade do que está acontecendo no sistema é essencial para o *debugging* e para garantir a saúde da aplicação.