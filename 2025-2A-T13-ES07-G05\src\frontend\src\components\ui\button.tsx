import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow-soft hover:shadow-medium hover:bg-primary/90 active:scale-[0.98]",
        destructive: "bg-danger text-danger-foreground shadow-soft hover:shadow-medium hover:bg-danger/90 active:scale-[0.98]",
        outline: "border border-border bg-background hover:bg-muted hover:border-primary/50 shadow-soft hover:shadow-medium",
        secondary: "bg-secondary text-secondary-foreground shadow-soft hover:shadow-medium hover:bg-secondary/80 active:scale-[0.98]",
        ghost: "hover:bg-muted hover:text-foreground border border-transparent hover:border-border/50",
        link: "text-primary underline-offset-4 hover:underline hover:text-primary/80",
        success: "bg-success text-success-foreground shadow-soft hover:shadow-medium hover:bg-success/90 active:scale-[0.98]",
        warning: "bg-warning text-warning-foreground shadow-soft hover:shadow-medium hover:bg-warning/90 active:scale-[0.98]",
        neutral: "bg-neutral text-neutral-foreground shadow-soft hover:shadow-medium hover:bg-neutral/90 active:scale-[0.98]",
      },
      size: {
        default: "h-10 px-4 py-2 text-sm rounded-xl",
        sm: "h-8 px-3 py-1.5 text-xs rounded-lg",
        lg: "h-12 px-6 py-3 text-base rounded-xl",
        icon: "h-10 w-10 rounded-xl",
        pill: "h-8 px-4 py-1.5 text-xs rounded-full",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />;
  },
);
Button.displayName = "Button";

export { Button, buttonVariants };
