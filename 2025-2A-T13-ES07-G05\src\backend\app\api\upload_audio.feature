Feature: Upload de arquivo de áudio
  Como usuário da API
  Quero enviar um arquivo de áudio MP3 ou WAV
  Para receber suas informações de nome, duração e tipo

  Scenario: Upload bem-sucedido de arquivo MP3
    Given um arquivo de áudio válido "test_audio.mp3" do tipo "audio/mpeg"
    When eu fizer POST para "/uploadaudio/"
    Then a resposta deve ter status 200
    And o campo "Arquivo" deve ser "test_audio.mp3"
    And o campo "Tipo de arquivo" deve ser "audio/mpeg"
    And o campo "Duração (minutos)" deve ser 3.45

  Scenario: Upload bem-sucedido de arquivo WAV
    Given um arquivo de áudio válido "test_audio.wav" do tipo "audio/wav"
    When eu fizer POST para "/uploadaudio/"
    Then a resposta deve ter status 200
    And o campo "Arquivo" deve ser "test_audio.wav"
    And o campo "Tipo de arquivo" deve ser "audio/wav"
    And o campo "Duração (minutos)" deve ser 2.5

  Scenario: Tipo de mídia não suportado
    Given um arquivo de áudio inválido "test.mp4" do tipo "audio/mp4"
    When eu fizer POST para "/uploadaudio/"
    Then a resposta deve ter status 415
    And o campo "detail.code" deve ser "Tipo de mídia não suportado"

  Scenario: Arquivo vazio
    Given um arquivo de áudio vazio "empty.mp3" do tipo "audio/mpeg"
    When eu fizer POST para "/uploadaudio/"
    Then a resposta deve ter status 400
    And o campo "detail.code" deve ser "Arquivo vazio"

  Scenario: Duração inválida
    Given um arquivo de áudio com duração inválida "bad_duration.mp3" do tipo "audio/mpeg"
    When eu fizer POST para "/uploadaudio/"
    Then a resposta deve ter status 400
    And o campo "detail.code" deve ser "Duração inválida"


