import os

ALLOWED_AUDIO_TYPES = ["audio/mpeg", "audio/wav", "audio/x-wav"]

# <PERSON><PERSON><PERSON><PERSON> onde salvaremos as transcrições .json
TRANSCRICOES_DIR = os.getenv("TRANSCRICOES_DIR", "./data/transcricoes")

# URL do webhook da API de avaliação (pode ser a própria app em localhost)
EVAL_WEBHOOK_URL = os.getenv("EVAL_WEBHOOK_URL", "http://localhost:8000/nlp/evaluate")

# Caminho do modelo treinado (se/quando você salvar via notebook)
MODEL_PATH = os.getenv("MODEL_PATH", "./src/nlp-model/model.pkl")

# (Opcional) MongoDB - deixaremos comentado no uso
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")
MONGO_DB = os.getenv("MONGO_DB", "comgas")
MONGO_COLLECTION = os.getenv("MONGO_COLLECTION", "avaliacoes")