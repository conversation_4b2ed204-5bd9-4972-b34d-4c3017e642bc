# Imagem base leve com Python 3.11
FROM python:3.11-slim

# Evita .pyc e melhora logs
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

# Instala o ffmpeg e dependências básicas
RUN apt-get update \
 && apt-get install -y --no-install-recommends ffmpeg curl ca-certificates build-essential \
 && rm -rf /var/lib/apt/lists/*

# Diretório de trabalho
WORKDIR /app

# Copia apenas requirements primeiro (melhor cache)
COPY requirements.txt /app/requirements.txt
RUN python -m pip install --upgrade pip \
 && pip install -r /app/requirements.txt

# Copia o código
# (ajuste se seu backend tiver outros arquivos ao lado de app/)
COPY app /app/app

# Define PYTHONPATH pra achar o `src` quando necessário
# aqui apontamos /app como raiz do pacote `app.*`
ENV PYTHONPATH=/app

# Porta do FastAPI
EXPOSE 8000

# Cria pasta para as transcrições dentro do container
RUN mkdir -p /app/data/transcricoes

# Usuário sem privilégios
RUN useradd -ms /bin/bash appuser
USER appuser

# Comando padrão (produção)
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
