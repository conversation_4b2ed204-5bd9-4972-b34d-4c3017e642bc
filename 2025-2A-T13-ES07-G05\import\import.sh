#!/bin/bash
# Script para importar CSV para MongoDB

# Espera o MongoDB iniciar
sleep 10

# Importa o CSV para a coleção 'conversas' no banco 'meubanco'
mongoimport \
  --host mongodb \
  --port 27017 \
  --username $MONGO_INITDB_ROOT_USERNAME \
  --password $MONGO_INITDB_ROOT_PASSWORD \
  --authenticationDatabase admin \
  --db $MONGO_INITDB_DATABASE \
  --collection conversas \
  --type csv \
  --headerline \
  --file /import/conversa.csv