"""Publish de uploads de áudio para RabbitMQ."""
from __future__ import annotations

import base64
import json
import os
import uuid
from datetime import datetime, timezone
from typing import Any, Dict

import pika
from pika.exceptions import AMQPConnectionError
from dotenv import load_dotenv

# Carrega variáveis do .env
load_dotenv()

class QueuePublishError(RuntimeError):
    pass

def _build_connection_parameters() -> pika.ConnectionParameters:
    host = os.getenv("RABBITMQ_HOST", "localhost")
    port = int(os.getenv("RABBITMQ_PORT", "5672"))
    vhost = os.getenv("RABBITMQ_VHOST", "/")
    user = os.getenv("RABBITMQ_USER", "guest")
    pwd = os.getenv("RABBITMQ_PASS", "guest")

    credentials = pika.PlainCredentials(user, pwd)
    return pika.ConnectionParameters(
        host=host,
        port=port,
        virtual_host=vhost,
        credentials=credentials,
        heartbeat=30,
        blocked_connection_timeout=30,
        socket_timeout=30,
        connection_attempts=5,
        retry_delay=2,
    )

def _ensure_queue(channel: pika.adapters.blocking_connection.BlockingChannel) -> str:
    queue = os.getenv("RABBITMQ_QUEUE", "transcription.audio")
    channel.queue_declare(queue=queue, durable=True)
    return queue

def enqueue_audio_for_transcription(
    *,
    file_bytes: bytes,
    filename: str,
    content_type: str,
    duration_seconds: float,
    client_ip: str | None = None,
) -> str:
    """Publica uma mensagem na fila com o áudio (base64) e metadados. Retorna o messageId."""
    message_id = str(uuid.uuid4())
    payload: Dict[str, Any] = {
        "messageId": message_id,
        "filename": filename,
        "contentType": content_type,
        "sizeBytes": len(file_bytes or b""),
        "durationMs": int(round(duration_seconds * 1000)),
        "audioContent": base64.b64encode(file_bytes).decode("ascii"),
        "createdAt": datetime.now(timezone.utc).isoformat(),
        "clientIp": client_ip or "",
    }

    params = _build_connection_parameters()
    try:
        print(f"[queue] Connecting to {params.host}:{params.port} vhost='{params.virtual_host}' user='{os.getenv('RABBITMQ_USER','guest')}'")
        with pika.BlockingConnection(params) as conn:
            ch = conn.channel()
            queue = _ensure_queue(ch)
            print(f"[queue] Declaring/ensuring queue '{queue}'")
            ch.basic_publish(
                exchange="",
                routing_key=queue,
                body=json.dumps(payload).encode("utf-8"),
                properties=pika.BasicProperties(
                    content_type="application/json",
                    delivery_mode=2,  # persistente
                    message_id=message_id,
                ),
                mandatory=False,  # evita UnroutableError
            )
        return message_id
    except AMQPConnectionError as exc:
        print("[queue] AMQP connection error:", repr(exc))
        raise QueuePublishError("Falha ao publicar na fila") from exc
    except Exception as exc:
        print("[queue] publish error:", repr(exc))
        raise QueuePublishError("Falha ao publicar na fila") from exc