import os, json, uuid, sys
import pika
from dotenv import load_dotenv
from pika.exceptions import AMQPConnectionError
from common.rabbitmq import get_connection, get_channel

load_dotenv()
QUEUE = os.getenv("RABBITMQ_QUEUE", "atendimento.ligacao")

def delivery():
    try:
        connection = get_connection()
        channel = get_channel(connection)

        
        channel.queue_declare(queue=QUEUE, durable=True)

        payload = {
            "_id": 1,
            "canal": "0800",
            "tags": ["suporte", "religacao"]
        }
        body = json.dumps(payload).encode("utf-8")

        props = pika.BasicProperties(
            delivery_mode=2,
            content_type="application/json",
            content_encoding="utf-8",
            message_id=str(uuid.uuid4()),
            correlation_id="req-1"
        )

        
        channel.confirm_delivery()

        
        def on_return(ch, method, properties, body):
            print(f"[!] UNROUTABLE: {method.reply_text}")

        channel.add_on_return_callback(on_return)

        ok = channel.basic_publish(
            exchange="",              
            routing_key=QUEUE,       
            body=body,
            properties=props,
            mandatory=True
        )
        if not ok:
            raise RuntimeError("<PERSON>roke<PERSON> did not confirm publish")

        print(f"[x] Sent: {payload['_id']}")
        connection.close()

    except AMQPConnectionError as e:
        print(f"Connection error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    delivery()

    


    
    
    

    



