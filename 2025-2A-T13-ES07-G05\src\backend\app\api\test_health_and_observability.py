import sys
from pathlib import Path
from http import HTTPStatus

from fastapi.testclient import TestClient

# Garantir path do backend
BACKEND_ROOT = Path(__file__).resolve().parents[2]
if str(BACKEND_ROOT) not in sys.path:
    sys.path.insert(0, str(BACKEND_ROOT))

try:
    from main import app
except ImportError:
    from app.main import app


def test_root_ok():
    client = TestClient(app)
    resp = client.get("/")
    assert resp.status_code == HTTPStatus.OK
    assert resp.json() == {"message": "Hello World"}


def test_health_ok_skipped_mongo(monkeypatch):
    client = TestClient(app)
    monkeypatch.delenv("MONGO_URI", raising=False)
    resp = client.get("/health")
    assert resp.status_code == HTTPStatus.OK
    body = resp.json()
    assert body["status"] in {"ok", "degraded"}
    assert body["mongo"]["status"] in {"skipped", "ok", "down"}
    assert "version" in body


def test_metrics_endpoint_available():
    client = TestClient(app)
    resp = client.get("/metrics")
    # Instrumentator deve expor 200 com texto
    assert resp.status_code == HTTPStatus.OK
    assert "http_request_duration" in resp.text or len(resp.text) > 0


def test_health_mongo_ok_and_version(monkeypatch):
    # Força MONGO_URI e injeta um MongoClient fake que responde ao ping
    monkeypatch.setenv("MONGO_URI", "**************************************")

    class _FakeAdmin:
        def command(self, name):
            assert name == "ping"
            return {"ok": 1}

    class _FakeClient:
        def __init__(self, uri, serverSelectionTimeoutMS):
            assert uri.startswith("mongodb://")
            self.admin = _FakeAdmin()

    # Patch no símbolo importado em app.health
    import app.health as health_mod
    monkeypatch.setattr(health_mod, "MongoClient", _FakeClient)

    # Garante versão definida
    monkeypatch.setenv("APP_VERSION", "1.2.3")

    client = TestClient(app)
    resp = client.get("/health")
    assert resp.status_code == HTTPStatus.OK
    data = resp.json()
    assert data["mongo"]["status"] == "ok"
    assert data["version"] == "1.2.3"


def test_configure_json_logging_no_duplicate_handlers():
    # Importa função privada para validar não duplicação de handlers
    from app.observability import _configure_json_logging

    logger_a = _configure_json_logging()
    handlers_count_before = len(logger_a.handlers)
    logger_b = _configure_json_logging()
    handlers_count_after = len(logger_b.handlers)
    # Deve manter a contagem ao reinvocar
    assert handlers_count_after == handlers_count_before
    assert handlers_count_after >= 1


def test_observability_error_path_middleware():
    # Cria app isolada com observabilidade e rota que lança erro
    from fastapi import FastAPI
    from app.observability import setup_observability

    error_app = FastAPI()
    setup_observability(error_app, enabled=True)

    @error_app.get("/boom")
    async def boom():  # noqa: F811
        raise RuntimeError("boom")

    client = TestClient(error_app, raise_server_exceptions=False)
    resp = client.get("/boom")
    # Middleware captura e reloga. Com raise_server_exceptions=False, retornamos 500
    assert resp.status_code == HTTPStatus.INTERNAL_SERVER_ERROR


def test_observability_disabled_flag():
    from fastapi import FastAPI
    from app.observability import setup_observability

    disabled_app = FastAPI()
    setup_observability(disabled_app, enabled=False)
    client = TestClient(disabled_app)
    # /metrics não deve existir
    resp = client.get("/metrics")
    assert resp.status_code == HTTPStatus.NOT_FOUND


def test_observability_disabled_env(monkeypatch):
    from fastapi import FastAPI
    from app.observability import setup_observability

    monkeypatch.setenv("OBSERVABILITY_ENABLED", "false")
    app_env = FastAPI()
    setup_observability(app_env)
    client = TestClient(app_env)
    resp = client.get("/metrics")
    assert resp.status_code == HTTPStatus.NOT_FOUND


def test_health_mongo_down(monkeypatch):
    # Simula exceção do Mongo para cobrir o bloco except
    import app.health as health_mod

    class _FakeAdmin:
        def command(self, name):
            raise health_mod.PyMongoError("simulated down")

    class _FakeClient:
        def __init__(self, uri, serverSelectionTimeoutMS):
            self.admin = _FakeAdmin()

    monkeypatch.setenv("MONGO_URI", "mongodb://localhost:27017/db")
    monkeypatch.setattr(health_mod, "MongoClient", _FakeClient)

    client = TestClient(app)
    resp = client.get("/health")
    assert resp.status_code == HTTPStatus.OK
    data = resp.json()
    assert data["mongo"]["status"] == "down"


