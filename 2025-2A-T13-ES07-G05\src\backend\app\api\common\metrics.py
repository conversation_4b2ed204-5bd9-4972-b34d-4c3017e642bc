from prometheus_client import Counter, Histogram, Gauge

# Transcrição
TRANSCRIPTIONS_TOTAL = Counter(
    "app_transcriptions_total", "Total de transcrições processadas", ["status"]
)
TRANSCRIPTION_DURATION_SECONDS = Histogram(
    "app_transcription_duration_seconds", "Duração da transcrição (s)"
)
TRANSCRIPTIONS_IN_FLIGHT = Gauge(
    "app_transcriptions_in_flight", "Transcrições em processamento"
)
TRANSCRIPTIONS_TOTAL_BY_TYPE = Counter(
    "app_transcriptions_total_by_type", "Total por tipo", ["status", "type"]
)
TRANSCRIPTION_SEGMENTS = Histogram(
    "app_transcription_segments", "Qtd de segmentos por transcrição"
)

# Webhook
WEBHOOK_FAILURES_TOTAL = Counter(
    "app_webhook_failures_total", "Falhas ao disparar webhook de avaliação"
)
WEBHOOK_LATENCY = Histogram(
    "app_webhook_latency_seconds", "Latência do POST de webhook de avaliação (s)"
)

# NLP
EVALUATIONS_TOTAL = Counter(
    "app_evaluations_total", "Total de avaliações NLP", ["status"]
)
EVALUATION_DURATION_SECONDS = Histogram(
    "app_evaluation_duration_seconds", "Duração da avaliação NLP (s)"
)
EVALUATIONS_IN_FLIGHT = Gauge(
    "app_evaluations_in_flight", "Avaliações em processamento"
)
EVALUATION_SCORE = Histogram(
    "app_evaluation_score", "Distribuição de notas (0-10)"
)

# Pipeline ponta-a-ponta
PIPELINE_E2E = Histogram(
    "app_pipeline_end_to_end_seconds",
    "Duração ponta-a-ponta: transcrição -> avaliação (s)"
)
