import io
import sys
from pathlib import Path
import pytest
from unittest.mock import MagicMock, patch
from fastapi.testclient import TestClient
from http import HTTPStatus

# Garante que o diretório raiz do backend (src/backend) esteja no sys.path
BACKEND_ROOT = Path(__file__).resolve().parents[2]
if str(BACKEND_ROOT) not in sys.path:
    sys.path.insert(0, str(BACKEND_ROOT))

try:
    from main import app
except ImportError:
    from app.main import app

client = TestClient(app)

AUDIO_ROUTE = "/uploadaudio/"
MP3_TYPE = "audio/mpeg"
WAV_TYPE = "audio/wav"

def mock_audio_file(duration_seconds: float):
    mock_audio = MagicMock()
    mock_audio.info.length = duration_seconds
    return mock_audio

def mock_MP3_success(file_like_object):
    return mock_audio_file(207.0)

def mock_WAVE_success(file_like_object):
    return mock_audio_file(150.0)

def mock_MP3_duration_error(file_like_object):
    return mock_audio_file(-5.0)


# AAA: Arrange, Act, Assert
@patch("app.api.routers.MP3", side_effect=mock_MP3_success)
@patch("app.api.routers.WAVE", side_effect=mock_WAVE_success)
def test_upload_audio_success_mp3(mock_wave, mock_mp3):
    # Arrange
    expected_duration = 3.45
    file_content = b"fake mp3 content" * 10
    file_name = "test_audio.mp3"
    files = {"audio_file": (file_name, io.BytesIO(file_content), MP3_TYPE)}

    # Act
    response = client.post(AUDIO_ROUTE, files=files)

    # Assert
    assert response.status_code == HTTPStatus.OK
    data = response.json()
    assert data["Arquivo"] == file_name
    assert data["Tipo de arquivo"] == MP3_TYPE
    assert data["Duração (minutos)"] == expected_duration
    mock_mp3.assert_called_once()
    mock_wave.assert_not_called()


@patch("app.api.routers.MP3", side_effect=mock_MP3_success)
@patch("app.api.routers.WAVE", side_effect=mock_WAVE_success)
def test_upload_audio_success_wav(mock_wave, mock_mp3):
    # Arrange
    expected_duration = 2.5
    file_content = b"fake wav content" * 10
    file_name = "test_audio.wav"
    files = {"audio_file": (file_name, io.BytesIO(file_content), WAV_TYPE)}

    # Act
    response = client.post(AUDIO_ROUTE, files=files)

    # Assert
    assert response.status_code == HTTPStatus.OK
    data = response.json()
    assert data["Arquivo"] == file_name
    assert data["Tipo de arquivo"] == WAV_TYPE
    assert data["Duração (minutos)"] == expected_duration
    mock_wave.assert_called_once()
    mock_mp3.assert_not_called()


def test_upload_audio_unsupported_media_type():
    # Arrange
    invalid_type = "audio/mp4"
    files = {"audio_file": ("test.mp4", io.BytesIO(b"content"), invalid_type)}

    # Act
    response = client.post(AUDIO_ROUTE, files=files)

    # Assert
    assert response.status_code == HTTPStatus.UNSUPPORTED_MEDIA_TYPE
    data = response.json()
    assert data["detail"]["code"] == "Tipo de mídia não suportado"
    assert "Apenas arquivos MP3 ou WAV são permitidos" in data["detail"]["message"]
    assert f"Recebido: {invalid_type}" in data["detail"]["message"]


@patch("app.api.routers.MP3", side_effect=mock_MP3_success)
def test_upload_audio_file_empty(mock_mp3):
    # Arrange
    file_content = b""
    files = {"audio_file": ("empty.mp3", io.BytesIO(file_content), MP3_TYPE)}

    # Act
    response = client.post(AUDIO_ROUTE, files=files)

    # Assert
    assert response.status_code == HTTPStatus.BAD_REQUEST
    data = response.json()
    assert data["detail"]["code"] == "Arquivo vazio"
    assert "O arquivo de áudio está vazio" in data["detail"]["message"]


@patch("app.api.routers.MP3", side_effect=mock_MP3_duration_error)
def test_upload_audio_invalid_duration(mock_mp3):
    # Arrange
    file_content = b"fake mp3 content" * 10
    files = {"audio_file": ("bad_duration.mp3", io.BytesIO(file_content), MP3_TYPE)}

    # Act
    response = client.post(AUDIO_ROUTE, files=files)

    # Assert
    assert response.status_code == HTTPStatus.BAD_REQUEST
    data = response.json()
    assert data["detail"]["code"] == "Duração inválida"
    assert "Não foi possível determinar a duração do áudio" in data["detail"]["message"]