{"cells": [{"cell_type": "markdown", "id": "3e462c44", "metadata": {}, "source": ["> **AVISO (execução no ambiente):** Este notebook foi projetado para **Google Colab**.  \n", "> No VS Code/venv local, a instalação/inicialização de **spaCy/transformers/torch (CUDA)** pode falhar por limitações da VM.  \n", "> Por favor, avalie **apenas o algoritmo e a modelagem** implementados.  \n", "> No Colab o pipeline executa integralmente seguindo a célula “Instalação de dependências”.  \n", "> Qualquer erro aqui é **de ambiente**, não de lógica do código.\n"]}, {"cell_type": "markdown", "id": "131c2763", "metadata": {}, "source": ["### **01: Instalação de dependências**\n", "\n", "Instala e prepara todas as bibliotecas necessárias: SpaCy, NLTK, scikit-learn, transformers, torch, pandas, unidecode.\n", "Também baixa o modelo pt_core_news_sm (SpaCy) e os recursos NLTK (stopwords e RSLP).\n", "Execute esta célula antes de qualquer outra para evitar erros de import ou falta de recursos linguísticos.\n", "Em ambientes com cache (como Colab), a instalação pode ser rápida; em locais limpos, pode levar alguns minutos.\n", "\n", "Carrega bibliotecas, configura o SpaCy e o NLTK (stopwords + stemmer RSLP) e define semente (SEED) para reprodutibilidade.\n", "A impressão do device confirma se há GPU disponível para o caminho transformer; caso contrá<PERSON>, o pipeline usa TF-IDF.\n", "Manter seeds fixas ajuda em auditorias e comparações entre execuções.\n", "Erros aqui geralmente indicam problemas na célula de instalação anterior."]}, {"cell_type": "code", "execution_count": 5, "id": "888e9074", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n", "Traceback (most recent call last):\n", "  File \"<frozen runpy>\", line 189, in _run_module_as_main\n", "  File \"<frozen runpy>\", line 148, in _get_module_details\n", "  File \"<frozen runpy>\", line 112, in _get_module_details\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/spacy/__init__.py\", line 6, in <module>\n", "    from .errors import setup_default_warnings\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/spacy/errors.py\", line 3, in <module>\n", "    from .compat import Literal\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/spacy/compat.py\", line 4, in <module>\n", "    from thinc.util import copy_array\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/thinc/__init__.py\", line 5, in <module>\n", "    from .config import registry\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/thinc/config.py\", line 5, in <module>\n", "    from .types import Decorator\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/thinc/types.py\", line 27, in <module>\n", "    from .compat import cupy, has_cupy\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/thinc/compat.py\", line 99, in <module>\n", "    import h5py\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/h5py/__init__.py\", line 45, in <module>\n", "    from ._conv import register_converters as _register_converters, \\\n", "  File \"h5py/_conv.pyx\", line 1, in init h5py._conv\n", "  File \"h5py/h5r.pyx\", line 1, in init h5py.h5r\n", "  File \"h5py/h5p.pyx\", line 1, in init h5py.h5p\n", "ValueError: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package stopwords to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package stopwords is already up-to-date!\n", "[nltk_data] Downloading package rslp to /Users/<USER>/nltk_data...\n", "[nltk_data]   Package rslp is already up-to-date!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["%pip install -q spacy nltk scikit-learn unidecode pandas transformers torch\n", "!python -m spacy download pt_core_news_sm\n", "\n", "import nltk\n", "nltk.download('stopwords')\n", "nltk.download('rslp')\n"]}, {"cell_type": "markdown", "id": "88e8d588", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 10, "id": "e6417b4c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting numpy\n", "  Using cached numpy-2.3.3-cp312-cp312-macosx_14_0_arm64.whl.metadata (62 kB)\n", "Using cached numpy-2.3.3-cp312-cp312-macosx_14_0_arm64.whl (5.1 MB)\n", "Installing collected packages: numpy\n", "  Attempting uninstall: numpy\n", "    Found existing installation: numpy 2.2.6\n", "    Uninstalling numpy-2.2.6:\n", "      Successfully uninstalled numpy-2.2.6\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "gensim 4.3.3 requires numpy<2.0,>=1.18.5, but you have numpy 2.3.3 which is incompatible.\n", "scipy 1.13.1 requires numpy<2.3,>=1.22.4, but you have numpy 2.3.3 which is incompatible.\n", "contourpy 1.2.0 requires numpy<2.0,>=1.20, but you have numpy 2.3.3 which is incompatible.\n", "numba 0.60.0 requires numpy<2.1,>=1.22, but you have numpy 2.3.3 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed numpy-2.3.3\n", "Note: you may need to restart the kernel to use updated packages.\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "gensim 4.3.3 requires numpy<2.0,>=1.18.5, but you have numpy 2.2.6 which is incompatible.\n", "contourpy 1.2.0 requires numpy<2.0,>=1.20, but you have numpy 2.2.6 which is incompatible.\n", "numba 0.60.0 requires numpy<2.1,>=1.22, but you have numpy 2.2.6 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}, {"ename": "ValueError", "evalue": "numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[10], line 8\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mre\u001b[39;00m\u001b[38;5;241m,\u001b[39m \u001b[38;5;21;01mtime\u001b[39;00m\u001b[38;5;241m,\u001b[39m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;241m,\u001b[39m \u001b[38;5;21;01mwarnings\u001b[39;00m\n\u001b[1;32m      5\u001b[0m warnings\u001b[38;5;241m.\u001b[39mfilterwarnings(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 8\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mspacy\u001b[39;00m\n\u001b[1;32m      9\u001b[0m nlp \u001b[38;5;241m=\u001b[39m spacy\u001b[38;5;241m.\u001b[39mload(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpt_core_news_sm\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mnltk\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcorpus\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m stopwords\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/spacy/__init__.py:6\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Any, Dict, Iterable, Union\n\u001b[1;32m      5\u001b[0m \u001b[38;5;66;03m# set library-specific custom warning handling before doing anything else\u001b[39;00m\n\u001b[0;32m----> 6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m setup_default_warnings\n\u001b[1;32m      8\u001b[0m setup_default_warnings()  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;66;03m# These are imported as part of the API\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/spacy/errors.py:3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mwarnings\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcompat\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Literal\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mErrorsWithCodes\u001b[39;00m(\u001b[38;5;28mtype\u001b[39m):\n\u001b[1;32m      7\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__getattribute__\u001b[39m(\u001b[38;5;28mself\u001b[39m, code):\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/spacy/compat.py:4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;124;03m\"\"\"Helpers for Python and platform compatibility.\"\"\"\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01msys\u001b[39;00m\n\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mthinc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutil\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m copy_array\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m      7\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/thinc/__init__.py:5\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mabout\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m __version__\n\u001b[0;32m----> 5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m registry\n\u001b[1;32m      7\u001b[0m \u001b[38;5;66;03m# fmt: off\u001b[39;00m\n\u001b[1;32m      8\u001b[0m __all__ \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mregistry\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__version__\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     11\u001b[0m ]\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/thinc/config.py:5\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mconfection\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mconfection\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m VARIABLE_RE, Config, ConfigValidationError, Promise\n\u001b[0;32m----> 5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Decorator\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mregistry\u001b[39;00m(confection\u001b[38;5;241m.\u001b[39mregistry):\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;66;03m# fmt: off\u001b[39;00m\n\u001b[1;32m     10\u001b[0m     optimizers: Decorator \u001b[38;5;241m=\u001b[39m catalogue\u001b[38;5;241m.\u001b[39mcreate(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mthinc\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124moptimizers\u001b[39m\u001b[38;5;124m\"\u001b[39m, entry_points\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/thinc/types.py:27\u001b[0m\n\u001b[1;32m     24\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpydantic\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m GetCoreSchemaHandler\n\u001b[1;32m     25\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpydantic_core\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m core_schema\n\u001b[0;32m---> 27\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcompat\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m cupy, has_cupy\n\u001b[1;32m     29\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m has_cupy:\n\u001b[1;32m     30\u001b[0m     get_array_module \u001b[38;5;241m=\u001b[39m cupy\u001b[38;5;241m.\u001b[39mget_array_module\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/thinc/compat.py:99\u001b[0m\n\u001b[1;32m     95\u001b[0m has_mxnet \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mF<PERSON>e\u001b[39;00m\n\u001b[1;32m     98\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 99\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mh5py\u001b[39;00m\n\u001b[1;32m    100\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:  \u001b[38;5;66;03m# pragma: no cover\u001b[39;00m\n\u001b[1;32m    101\u001b[0m     h5py \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/h5py/__init__.py:45\u001b[0m\n\u001b[1;32m     36\u001b[0m     _warn((\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mh5py is running against HDF5 \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;124m when it was built against \u001b[39m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     37\u001b[0m            \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mthis may cause problems\u001b[39m\u001b[38;5;124m\"\u001b[39m)\u001b[38;5;241m.\u001b[39mformat(\n\u001b[1;32m     38\u001b[0m             \u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\u001b[38;5;241m*\u001b[39mversion\u001b[38;5;241m.\u001b[39mhdf5_version_tuple),\n\u001b[1;32m     39\u001b[0m             \u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\u001b[38;5;241m*\u001b[39mversion\u001b[38;5;241m.\u001b[39mhdf5_built_version_tuple)\n\u001b[1;32m     40\u001b[0m     ))\n\u001b[1;32m     43\u001b[0m _errors\u001b[38;5;241m.\u001b[39msilence_errors()\n\u001b[0;32m---> 45\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_conv\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m register_converters \u001b[38;5;28;01mas\u001b[39;00m _register_converters, \\\n\u001b[1;32m     46\u001b[0m                    unregister_converters \u001b[38;5;28;01mas\u001b[39;00m _unregister_converters\n\u001b[1;32m     47\u001b[0m _register_converters()\n\u001b[1;32m     48\u001b[0m atexit\u001b[38;5;241m.\u001b[39mregister(_unregister_converters)\n", "File \u001b[0;32mh5py/_conv.pyx:1\u001b[0m, in \u001b[0;36minit h5py._conv\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mh5py/h5r.pyx:1\u001b[0m, in \u001b[0;36minit h5py.h5r\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mh5py/h5p.pyx:1\u001b[0m, in \u001b[0;36minit h5py.h5p\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject"]}], "source": ["%pip install --force-reinstall numpy\n", "%pip install -q spacy nltk scikit-learn unidecode pandas transformers torch\n", "\n", "import re, time, random, warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from unidecode import unidecode\n", "\n", "import spacy\n", "nlp = spacy.load('pt_core_news_sm')\n", "from nltk.corpus import stopwords\n", "from nltk.stem import RSLPStemmer\n", "stop_words = set(stopwords.words('portuguese'))\n", "stemmer = RSLPStemmer()\n", "\n", "from sklearn.pipeline import Pipeline, FeatureUnion\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.linear_model import LogisticRegression, Ridge\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "import torch\n", "from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments\n", "from transformers import AutoConfig\n", "\n", "SEED = 42\n", "random.seed(SEED)\n", "np.random.seed(SEED)\n", "if torch.cuda.is_available():\n", "    torch.manual_seed(SEED)\n", "    torch.cuda.manual_seed_all(SEED)\n", "\n", "DEVICE = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "print(\"Device:\", DEVICE)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "4dbb04ac", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "A module that was compiled using NumPy 1.x cannot be run in\n", "NumPy 2.2.6 as it may crash. To support both 1.x and 2.x\n", "versions of NumPy, modules must be compiled with NumPy 2.0.\n", "Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n", "\n", "If you are a user of the module, the easiest solution will be to\n", "downgrade to 'numpy<2' or try to upgrade the affected module.\n", "We expect that some modules will need time to support NumPy 2.\n", "\n", "Traceback (most recent call last):  File \"<frozen runpy>\", line 198, in _run_module_as_main\n", "  File \"<frozen runpy>\", line 88, in _run_code\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/ipykernel_launcher.py\", line 17, in <module>\n", "    app.launch_new_instance()\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/ipykernel/kernelapp.py\", line 701, in start\n", "    self.io_loop.start()\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"/opt/anaconda3/lib/python3.12/asyncio/base_events.py\", line 641, in run_forever\n", "    self._run_once()\n", "  File \"/opt/anaconda3/lib/python3.12/asyncio/base_events.py\", line 1986, in _run_once\n", "    handle._run()\n", "  File \"/opt/anaconda3/lib/python3.12/asyncio/events.py\", line 88, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/ipykernel/kernelbase.py\", line 534, in dispatch_queue\n", "    await self.process_one()\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/ipykernel/kernelbase.py\", line 523, in process_one\n", "    await dispatch(*args)\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/ipykernel/kernelbase.py\", line 429, in dispatch_shell\n", "    await result\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/ipykernel/kernelbase.py\", line 767, in execute_request\n", "    reply_content = await reply_content\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/ipykernel/ipkernel.py\", line 429, in do_execute\n", "    res = shell.run_cell(\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/var/folders/t9/xpq30pr56q5f11hy8ylh_vcr0000gn/T/ipykernel_17894/3961178866.py\", line 2, in <module>\n", "    import pandas as pd\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/pandas/__init__.py\", line 49, in <module>\n", "    from pandas.core.api import (\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/pandas/core/api.py\", line 1, in <module>\n", "    from pandas._libs import (\n", "  File \"/opt/anaconda3/lib/python3.12/site-packages/pandas/_libs/__init__.py\", line 17, in <module>\n", "    import pandas._libs.pandas_datetime  # noqa: F401 # isort: skip # type: ignore[reportUnusedImport]\n"]}, {"ename": "ImportError", "evalue": "\nA module that was compiled using NumPy 1.x cannot be run in\nNumPy 2.2.6 as it may crash. To support both 1.x and 2.x\nversions of NumPy, modules must be compiled with NumPy 2.0.\nSome module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n\nIf you are a user of the module, the easiest solution will be to\ndowngrade to 'numpy<2' or try to upgrade the affected module.\nWe expect that some modules will need time to support NumPy 2.\n\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/numpy/core/_multiarray_umath.py:44\u001b[0m, in \u001b[0;36m__getattr__\u001b[0;34m(attr_name)\u001b[0m\n\u001b[1;32m     39\u001b[0m     \u001b[38;5;66;03m# Also print the message (with traceback).  This is because old versions\u001b[39;00m\n\u001b[1;32m     40\u001b[0m     \u001b[38;5;66;03m# of NumPy unfortunately set up the import to replace (and hide) the\u001b[39;00m\n\u001b[1;32m     41\u001b[0m     \u001b[38;5;66;03m# error.  The traceback shouldn't be needed, but e.g. pytest plugins\u001b[39;00m\n\u001b[1;32m     42\u001b[0m     \u001b[38;5;66;03m# seem to swallow it and we should be failing anyway...\u001b[39;00m\n\u001b[1;32m     43\u001b[0m     sys\u001b[38;5;241m.\u001b[39mstderr\u001b[38;5;241m.\u001b[39mwrite(msg \u001b[38;5;241m+\u001b[39m tb_msg)\n\u001b[0;32m---> 44\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>se\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m(msg)\n\u001b[1;32m     46\u001b[0m ret \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(_multiarray_umath, attr_name, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m     47\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m ret \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[0;31mImportError\u001b[0m: \nA module that was compiled using NumPy 1.x cannot be run in\nNumPy 2.2.6 as it may crash. To support both 1.x and 2.x\nversions of NumPy, modules must be compiled with NumPy 2.0.\nSome module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n\nIf you are a user of the module, the easiest solution will be to\ndowngrade to 'numpy<2' or try to upgrade the affected module.\nWe expect that some modules will need time to support NumPy 2.\n\n"]}, {"ename": "ImportError", "evalue": "numpy.core.multiarray failed to import", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[12], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01munidecode\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m unidecode\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mspacy\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/pandas/__init__.py:49\u001b[0m\n\u001b[1;32m     46\u001b[0m \u001b[38;5;66;03m# let init-time option registration happen\u001b[39;00m\n\u001b[1;32m     47\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig_init\u001b[39;00m  \u001b[38;5;66;03m# pyright: ignore[reportUnusedImport] # noqa: F401\u001b[39;00m\n\u001b[0;32m---> 49\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01ma<PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     50\u001b[0m     \u001b[38;5;66;03m# dtype\u001b[39;00m\n\u001b[1;32m     51\u001b[0m     ArrowDtype,\n\u001b[1;32m     52\u001b[0m     Int8Dtype,\n\u001b[1;32m     53\u001b[0m     Int16Dtype,\n\u001b[1;32m     54\u001b[0m     Int32Dtype,\n\u001b[1;32m     55\u001b[0m     Int64Dtype,\n\u001b[1;32m     56\u001b[0m     UInt8Dtype,\n\u001b[1;32m     57\u001b[0m     UInt16Dtype,\n\u001b[1;32m     58\u001b[0m     UInt32Dtype,\n\u001b[1;32m     59\u001b[0m     UInt64Dtype,\n\u001b[1;32m     60\u001b[0m     Float32Dtype,\n\u001b[1;32m     61\u001b[0m     Float64Dtype,\n\u001b[1;32m     62\u001b[0m     CategoricalDtype,\n\u001b[1;32m     63\u001b[0m     PeriodDtype,\n\u001b[1;32m     64\u001b[0m     IntervalDtype,\n\u001b[1;32m     65\u001b[0m     DatetimeTZDtype,\n\u001b[1;32m     66\u001b[0m     StringDtype,\n\u001b[1;32m     67\u001b[0m     BooleanDtype,\n\u001b[1;32m     68\u001b[0m     \u001b[38;5;66;03m# missing\u001b[39;00m\n\u001b[1;32m     69\u001b[0m     NA,\n\u001b[1;32m     70\u001b[0m     isna,\n\u001b[1;32m     71\u001b[0m     isnull,\n\u001b[1;32m     72\u001b[0m     notna,\n\u001b[1;32m     73\u001b[0m     notnull,\n\u001b[1;32m     74\u001b[0m     \u001b[38;5;66;03m# indexes\u001b[39;00m\n\u001b[1;32m     75\u001b[0m     Index,\n\u001b[1;32m     76\u001b[0m     CategoricalIndex,\n\u001b[1;32m     77\u001b[0m     RangeIndex,\n\u001b[1;32m     78\u001b[0m     MultiIndex,\n\u001b[1;32m     79\u001b[0m     IntervalIndex,\n\u001b[1;32m     80\u001b[0m     TimedeltaIndex,\n\u001b[1;32m     81\u001b[0m     DatetimeIndex,\n\u001b[1;32m     82\u001b[0m     PeriodIndex,\n\u001b[1;32m     83\u001b[0m     IndexSlice,\n\u001b[1;32m     84\u001b[0m     \u001b[38;5;66;03m# tseries\u001b[39;00m\n\u001b[1;32m     85\u001b[0m     NaT,\n\u001b[1;32m     86\u001b[0m     Period,\n\u001b[1;32m     87\u001b[0m     period_range,\n\u001b[1;32m     88\u001b[0m     Timedelta,\n\u001b[1;32m     89\u001b[0m     timedelta_range,\n\u001b[1;32m     90\u001b[0m     Timestamp,\n\u001b[1;32m     91\u001b[0m     date_range,\n\u001b[1;32m     92\u001b[0m     bdate_range,\n\u001b[1;32m     93\u001b[0m     Interval,\n\u001b[1;32m     94\u001b[0m     interval_range,\n\u001b[1;32m     95\u001b[0m     DateOffset,\n\u001b[1;32m     96\u001b[0m     \u001b[38;5;66;03m# conversion\u001b[39;00m\n\u001b[1;32m     97\u001b[0m     to_numeric,\n\u001b[1;32m     98\u001b[0m     to_datetime,\n\u001b[1;32m     99\u001b[0m     to_timedelta,\n\u001b[1;32m    100\u001b[0m     \u001b[38;5;66;03m# misc\u001b[39;00m\n\u001b[1;32m    101\u001b[0m     Flags,\n\u001b[1;32m    102\u001b[0m     Grouper,\n\u001b[1;32m    103\u001b[0m     factorize,\n\u001b[1;32m    104\u001b[0m     unique,\n\u001b[1;32m    105\u001b[0m     value_counts,\n\u001b[1;32m    106\u001b[0m     NamedAgg,\n\u001b[1;32m    107\u001b[0m     array,\n\u001b[1;32m    108\u001b[0m     Categorical,\n\u001b[1;32m    109\u001b[0m     set_eng_float_format,\n\u001b[1;32m    110\u001b[0m     Series,\n\u001b[1;32m    111\u001b[0m     DataFrame,\n\u001b[1;32m    112\u001b[0m )\n\u001b[1;32m    114\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m SparseDtype\n\u001b[1;32m    116\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtseries\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m infer_freq\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/pandas/core/api.py:1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m      2\u001b[0m     NaT,\n\u001b[1;32m      3\u001b[0m     Period,\n\u001b[1;32m      4\u001b[0m     <PERSON>del<PERSON>,\n\u001b[1;32m      5\u001b[0m     Timestamp,\n\u001b[1;32m      6\u001b[0m )\n\u001b[1;32m      7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmissing\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m NA\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     10\u001b[0m     ArrowDtype,\n\u001b[1;32m     11\u001b[0m     CategoricalDtype,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     14\u001b[0m     PeriodDtype,\n\u001b[1;32m     15\u001b[0m )\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/pandas/_libs/__init__.py:17\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m# Below imports needs to happen first to ensure pandas top level\u001b[39;00m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;66;03m# module gets monkeypatched with the pandas_datetime_CAPI\u001b[39;00m\n\u001b[1;32m     15\u001b[0m \u001b[38;5;66;03m# see pandas_datetime_exec in pd_datetime.c\u001b[39;00m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpandas_parser\u001b[39;00m  \u001b[38;5;66;03m# isort: skip # type: ignore[reportUnusedImport]\u001b[39;00m\n\u001b[0;32m---> 17\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpandas_datetime\u001b[39;00m  \u001b[38;5;66;03m# noqa: F401 # isort: skip # type: ignore[reportUnusedImport]\u001b[39;00m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01minterval\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Interval\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtslibs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     20\u001b[0m     NaT,\n\u001b[1;32m     21\u001b[0m     NaTType,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     26\u001b[0m     iNaT,\n\u001b[1;32m     27\u001b[0m )\n", "\u001b[0;31mImportError\u001b[0m: numpy.core.multiarray failed to import"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from unidecode import unidecode\n", "\n", "import spacy\n", "nlp = spacy.load('pt_core_news_sm')\n", "from nltk.corpus import stopwords\n", "from nltk.stem import RSLPStemmer\n", "stop_words = set(stopwords.words('portuguese'))\n", "stemmer = RSLPStemmer()\n", "\n", "from sklearn.pipeline import Pipeline, FeatureUnion\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.linear_model import LogisticRegression, Ridge\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "import torch\n", "from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments\n", "from transformers import AutoConfig"]}, {"cell_type": "markdown", "id": "0d59fce8", "metadata": {}, "source": ["### **02 -Criteria (regex – canal 0800)**\n", "\n", "Define a tabela de critérios de qualidade (C01–C11) com campos: code, name, type (must/should), weight, who, regex.\n", "O dicionário CRITERIA_BY_CHANNEL permite dinamismo por canal; get_criteria(channel) retorna a lista correta (default 0800).\n", "As regex são aplicadas somente ao speaker indicado em who (agent/user), reforçando a aferição de responsabilidade.\n", "Ajuste pesos com cautela: eles impactam a nota final e a sensibilidade às falhas MUST/SHOULD."]}, {"cell_type": "code", "execution_count": null, "id": "1e641dea", "metadata": {}, "outputs": [], "source": ["criteria_0800 = [\n", "    {\"code\":\"C01\",\"name\":\"Saudação inicial adequada (0800)\",\"type\":\"should\",\"weight\":1.0,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(ol[áa]|oi|bom dia|boa tarde|boa noite)\\b\"},\n", "    {\"code\":\"C02\",\"name\":\"Identificação do agente (Comgás)\",\"type\":\"should\",\"weight\":0.7,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(meu nome (é|e)|falo com|da comg[aá]s)\\b\"},\n", "    {\"code\":\"C03\",\"name\":\"Solicitação de confirmação de dados\",\"type\":\"must\",\"weight\":1.5,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(confirmar|confirme|poderia confirmar).*(dados|nome completo|data de nascimento)\\b\"},\n", "    {\"code\":\"C04\",\"name\":\"Validação (cliente: nome + data)\",\"type\":\"must\",\"weight\":1.8,\"who\":\"user\",\n", "     \"regex\": r\"\\b(sou|meu nome (é|e))\\b.*\\b(0[1-9]|[12][0-9]|3[01])[\\/\\.\\-](0[1-9]|1[0-2])[\\/\\.\\-](19|20)\\d{2}\\b\"},\n", "    {\"code\":\"C05\",\"name\":\"Empatia / reconhecimento\",\"type\":\"should\",\"weight\":0.8,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(entendo|compreendo|sinto muito|entendi sua preocupa[cç][aã]o)\\b\"},\n", "    {\"code\":\"C06\",\"name\":\"Esclarece causa/diagnóstico\",\"type\":\"should\",\"weight\":1.0,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(identifiquei|verifiquei|constat(ei|amos)|houve cobran[çc]a de m[eé]dia|causa)\\b\"},\n", "    {\"code\":\"C07\",\"name\":\"Abre OS/protocolo\",\"type\":\"must\",\"weight\":1.7,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(abrir|abrirei|abr(i|o) )\\s*(uma )?(os|ordem de servi[cç]o|solicita[cç][aã]o|protocolo)|solicita[cç][aã]o aberta|protocolo\\b\"},\n", "    {\"code\":\"C08\",\"name\":\"SLA/prazo comunicado\",\"type\":\"must\",\"weight\":1.4,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(\\d{1,2})\\s*(dia|dias|hora|horas)\\b.*(ú<PERSON>|ú<PERSON><PERSON>)?\\b\"},\n", "    {\"code\":\"C09\",\"name\":\"Checagem de clareza\",\"type\":\"should\",\"weight\":1.0,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(ficou claro|est[aá] claro|posso abrir a solicita[cç][aã]o)\\b\"},\n", "    {\"code\":\"C10\",\"name\":\"Encerramento + oferta de ajuda\",\"type\":\"should\",\"weight\":1.2,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(posso ajudar em (mais|algo mais)|mais alguma coisa)\\b\"},\n", "    {\"code\":\"C11\",\"name\":\"Convite à pesquisa de satisfação\",\"type\":\"should\",\"weight\":0.7,\"who\":\"agent\",\n", "     \"regex\": r\"\\b(pesquisa de satisfa[cç][aã]o)\\b\"},\n", "]\n", "\n", "CRITERIA_BY_CHANNEL = {\"0800\": criteria_0800}\n", "\n", "def get_criteria(channel:str):\n", "    return CRITERIA_BY_CHANNEL.get(channel, criteria_0800)"]}, {"cell_type": "markdown", "id": "effa640d", "metadata": {}, "source": ["### **03 - Pré-processamento e builders**\n", "\n", "preprocess_text normaliza o texto (lower, sem acentos, caracteres não alfabéticos, espaço único) e aplica stemmer/lemma conforme indicado.\n", "concat_messages junta, na ordem, todos os text das mensagens para formar um documento por atendimento.\n", "build_df_from_atendimentos constrói DataFrames para regressão (nota) e classificação (label binário ≥6 vs ≤5).\n", "A coluna text_proc facilita depuração; o TF-IDF internamente já normaliza, mas manter essa coluna pode ajudar em análises."]}, {"cell_type": "code", "execution_count": null, "id": "0504f9b3", "metadata": {}, "outputs": [], "source": ["def preprocess_text(text, use_lemma=False):\n", "    if text is None:\n", "        text = \"\"\n", "    elif not isinstance(text, str):\n", "        try:\n", "            text = str(text)\n", "        except Exception:\n", "            text = \"\"\n", "\n", "    text = unidecode(text.lower())\n", "    text = re.sub(r'[^a-z\\s]', ' ', text)\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "\n", "    if use_lemma:\n", "        tokens = nlp(text)\n", "        processed_tokens = [token.lemma_ for token in tokens if token.text not in stop_words and not token.is_space]\n", "    else:\n", "        tokens = text.split()\n", "        processed_tokens = [stemmer.stem(word) for word in tokens if word not in stop_words]\n", "\n", "    return ' '.join(processed_tokens)\n", "\n", "def concat_messages(atendimento:dict)->str:\n", "    return ' '.join(m.get('text','') for m in atendimento.get('messages', []))\n", "\n", "def build_df_from_atendimentos(atendimentos:list, task='regression')->pd.DataFrame:\n", "    rows = []\n", "    for a in atendimentos:\n", "        text = concat_messages(a)\n", "        nota = a.get('nota', None)\n", "        row = {\"_id\": a.get(\"_id\",\"<sem_id>\"), \"text\": text}\n", "        if nota is not None:\n", "            row[\"nota\"] = float(nota)\n", "            if task == 'classification':\n", "                row[\"label\"] = 1 if float(nota) >= 6.0 else 0\n", "            else:\n", "                row[\"label\"] = float(nota)\n", "        rows.append(row)\n", "    df = pd.DataFrame(rows)\n", "\n", "    df[\"text_proc\"] = df[\"text\"].astype(str).apply(lambda s: preprocess_text(s, use_lemma=False))\n", "    return df"]}, {"cell_type": "markdown", "id": "e03c96be", "metadata": {}, "source": ["### **04 - Helpers (_by_speaker, regex_match_map, score de regras)**\n", "\n", "_by_speaker separa e concatena falas de agent e user; isso viabiliza regex por who.\n", "regex_match_map devolve um dicionário {code: 0/1} indicando se cada critério foi detectado nas falas do quem correto.\n", "compute_rule_only_score gera um subscore 0–10 usando apenas as regras, ponderando pelos weights.\n", "Esses utilitários são essenciais para explicabilidade e para a composição do score híbrido."]}, {"cell_type": "code", "execution_count": null, "id": "7503f4f1", "metadata": {}, "outputs": [], "source": ["def _by_speaker(transcricao):\n", "    if isinstance(transcricao, dict) and 'messages' in transcricao:\n", "        agent_texts, user_texts = [], []\n", "        for msg in transcricao['messages']:\n", "            spk = (msg.get('speaker') or '').lower().strip()\n", "            txt = msg.get('text') or ''\n", "            if spk == 'agent':\n", "                agent_texts.append(txt)\n", "            elif spk == 'user':\n", "                user_texts.append(txt)\n", "        agent = ' '.join(agent_texts)\n", "        user  = ' '.join(user_texts)\n", "        return {'agent': unidecode(agent.lower()), 'user': unidecode(user.lower()), 'all': unidecode((agent + ' ' + user).lower())}\n", "    else:\n", "        s = unidecode(str(transcricao or '').lower())\n", "        return {'agent': s, 'user': s, 'all': s}\n", "\n", "def regex_match_map(texts_by_speaker, criteria)->dict:\n", "    out = {}\n", "    for c in criteria:\n", "        patt = c[\"regex\"]\n", "        who = c[\"who\"].lower()\n", "        hay = texts_by_speaker.get(who, texts_by_speaker[\"all\"])\n", "        out[c[\"code\"]] = 1 if re.search(patt, hay) else 0\n", "    return out\n", "\n", "def compute_rule_only_score(texts_by_speaker, criteria)->float:\n", "\n", "    total_w = sum(float(c[\"weight\"]) for c in criteria)\n", "    if total_w <= 0:\n", "        return 0.0\n", "    s = 0.0\n", "    for c in criteria:\n", "        w = float(c[\"weight\"])\n", "        who = c[\"who\"].lower()\n", "        hay = texts_by_speaker.get(who, texts_by_speaker[\"all\"])\n", "        rule = 1 if re.search(c[\"regex\"], hay) else 0\n", "        s += rule * w\n", "    return round(10.0 * (s / total_w), 2)"]}, {"cell_type": "markdown", "id": "2e5fb125", "metadata": {}, "source": ["### **05 - evaluate_transcription (regex + ML, sem zerar)**\n", "\n", "Combina ML e regras por critério: crit_score = α*ml_pos + (1-α)*rule_match, agregando por weight.\n", "ml_pos vem do classificador (probabilidade positiva) ou do regressor normalizado (nota/10), com fallback 0.5.\n", "Aplica penalidade suave por cada MUST não atendido: final = base * max(0.1, 1 - penalty_must*k); nunca zera.\n", "Ative debug=True para ver, por critério: (code, type, who, rule, ml_pos, crit_score) e entender a construção da nota."]}, {"cell_type": "code", "execution_count": null, "id": "32efdb9c", "metadata": {}, "outputs": [], "source": ["def evaluate_transcription(\n", "    transcricao:dict,\n", "    criteria_list:list,\n", "    models:dict=None,\n", "    alpha_ml:float=0.6,\n", "    penalty_must:float=0.20,\n", "    debug:bool=False\n", ")->float:\n", "\n", "    texts = _by_speaker(transcricao)\n", "    total_weight = sum(float(c['weight']) for c in criteria_list)\n", "    if total_weight <= 0:\n", "        return 0.0\n", "\n", "\n", "    def ml_pos_proba(text_all:str)->float:\n", "        if models is None:\n", "            return 0.5\n", "\n", "        if \"general_clf\" in models and models[\"general_clf\"] is not None:\n", "            pipe = models[\"general_clf\"]\n", "            try:\n", "                proba = pipe.predict_proba([text_all])[0]\n", "                return float(proba[1]) if len(proba)==2 else float(max(proba))\n", "            except Exception:\n", "                pass\n", "\n", "        if \"general_reg\" in models and models[\"general_reg\"] is not None:\n", "            pipe = models[\"general_reg\"]\n", "            try:\n", "                y = float(pipe.predict([text_all])[0])\n", "                return float(max(0.0, min(10.0, y)) / 10.0)\n", "            except Exception:\n", "                pass\n", "        return 0.5\n", "\n", "    ml_pos = ml_pos_proba(texts[\"all\"])\n", "\n", "\n", "    raw_sum = 0.0\n", "    must_fail_count = 0\n", "    debug_rows = []\n", "    for c in criteria_list:\n", "        who = c[\"who\"].lower()\n", "        w   = float(c[\"weight\"])\n", "        patt= c[\"regex\"]\n", "        hay = texts.get(who, texts[\"all\"])\n", "\n", "        rule = 1 if re.search(patt, hay) else 0\n", "        crit_score = alpha_ml*ml_pos + (1-alpha_ml)*rule\n", "        raw_sum += crit_score * w\n", "\n", "        if c[\"type\"].lower() == \"must\" and rule == 0:\n", "            must_fail_count += 1\n", "\n", "        if debug:\n", "            debug_rows.append((c[\"code\"], c[\"type\"], who, rule, round(ml_pos,3), round(crit_score,3)))\n", "\n", "    base_score = 10.0 * (raw_sum / total_weight)\n", "    base_score = max(0.0, min(10.0, base_score))\n", "\n", "\n", "    if must_fail_count > 0:\n", "        penalty_multiplier = max(0.1, 1.0 - penalty_must * must_fail_count)\n", "        final_score = base_score * penalty_multiplier\n", "    else:\n", "        final_score = base_score\n", "\n", "    final_score = round(max(0.0, min(10.0, final_score)), 2)\n", "\n", "    if debug:\n", "        print(\"DEBUG (code, type, who, rule, ml_pos, crit_score):\")\n", "        for row in debug_rows:\n", "            print(row)\n", "        print(f\"Base={base_score:.2f} | MUST_falhos={must_fail_count} | Final={final_score:.2f}\")\n", "\n", "    return final_score\n"]}, {"cell_type": "markdown", "id": "381fd528", "metadata": {}, "source": ["### **06 - <PERSON><PERSON><PERSON> dinâmica e treino de modelos**\n", "\n", "auto_select_strategy escolhe TF-IDF linear, TF-IDF+GBM ou Transformer conforme n_samples e GPU.\n", "build_vectorizer monta um TF-IDF híbrido (palavra 1–2 e caractere 3–5) com sublinear_tf, ótimo para PT-BR curto.\n", "auto_train_model treina classificação e regressão quando possível; no modo transformer, cria wrapper de predição.\n", "Há fallback automático para TF-IDF linear se o transformer falhar ou não houver GPU/dados suficientes."]}, {"cell_type": "code", "execution_count": null, "id": "2956d5fd", "metadata": {}, "outputs": [], "source": ["def auto_select_strategy(n_samples:int):\n", "    if n_samples >= 20000 and torch.cuda.is_available():\n", "        return {\"mode\":\"transformer\", \"task\":\"regression\"}\n", "    elif 1000 <= n_samples < 20000:\n", "        return {\"mode\":\"tfidf_gbm\", \"task\":\"classification\"}\n", "    else:\n", "        return {\"mode\":\"tfidf_linear\", \"task\":\"classification\"}  # e também treinar reg simples\n", "\n", "def build_vectorizer():\n", "    word_vec = TfidfVectorizer(analyzer=\"word\", ngram_range=(1,2), sublinear_tf=True, min_df=1, norm='l2')\n", "    char_vec = TfidfVectorizer(analyzer=\"char\", ngram_range=(3,5), sublinear_tf=True, min_df=1, norm='l2')\n", "    return FeatureUnion([(\"w\", word_vec), (\"c\", char_vec)])\n", "\n", "def auto_train_model(df_clf:pd.DataFrame, df_reg:pd.DataFrame, strategy:dict):\n", "    mode = strategy[\"mode\"]\n", "    models = {\"general_clf\": None, \"general_reg\": None, \"vectorizer\": None, \"transformer\": None}\n", "    print(\"Estratégia:\", strategy)\n", "\n", "    if mode in (\"tfidf_linear\", \"tfidf_gbm\"):\n", "\n", "        if df_clf is not None and \"label\" in df_clf.columns:\n", "            vec = build_vectorizer()\n", "            X = df_clf[\"text\"].astype(str).tolist()\n", "            y = df_clf[\"label\"].astype(int).values\n", "\n", "            if mode == \"tfidf_linear\":\n", "                clf = LogisticRegression(max_iter=1000, C=2.0)\n", "                pipe_clf = Pipeline([(\"vec\", vec), (\"clf\", clf)])\n", "            else:\n", "                rf = RandomForestClassifier(n_estimators=300, random_state=SEED, n_jobs=-1)\n", "                pipe_clf = Pipeline([(\"vec\", vec), (\"clf\", rf)])\n", "\n", "            print(\"Treinando classificador...\")\n", "            t0 = time.time()\n", "            pipe_clf.fit(X, y)\n", "            print(f\"OK (clf) em {time.time()-t0:.2f}s\")\n", "            models[\"general_clf\"] = pipe_clf\n", "            models[\"vectorizer\"]  = vec\n", "\n", "\n", "        if df_reg is not None and \"label\" in df_reg.columns:\n", "            vec_r = build_vectorizer()\n", "            Xr = df_reg[\"text\"].astype(str).tolist()\n", "            yr = df_reg[\"label\"].astype(float).values\n", "\n", "            reg = Ridge(alpha=1.0, random_state=SEED)\n", "            pipe_reg = Pipeline([(\"vec\", vec_r), (\"reg\", reg)])\n", "            print(\"Tre<PERSON><PERSON> regressor (nota 0–10)...\")\n", "            t0 = time.time()\n", "            pipe_reg.fit(Xr, yr)\n", "            print(f\"OK (reg) em {time.time()-t0:.2f}s | MAE treino: {mean_absolute_error(yr, pipe_reg.predict(Xr)):.3f}\")\n", "            models[\"general_reg\"] = pipe_reg\n", "        return models\n", "\n", "\n", "    if mode == \"transformer\":\n", "        model_name = \"neuralmind/bert-base-portuguese-cased\"\n", "        try:\n", "            cfg = AutoConfig.from_pretrained(model_name, num_labels=1, problem_type=\"regression\")\n", "            tok = AutoTokenizer.from_pretrained(model_name)\n", "            model = AutoModelForSequenceClassification.from_pretrained(model_name, config=cfg).to(DEVICE)\n", "\n", "\n", "            class TxtDS(torch.utils.data.Dataset):\n", "                def __init__(self, texts, labels):\n", "                    self.texts = texts\n", "                    self.labels = labels\n", "                def __len__(self): return len(self.texts)\n", "                def __getitem__(self, idx):\n", "                    enc = tok(self.texts[idx], truncation=True, padding=\"max_length\", max_length=256)\n", "                    item = {k: torch.tensor(v) for k,v in enc.items()}\n", "                    if self.labels is not None:\n", "                        item[\"labels\"] = torch.tensor([float(self.labels[idx])], dtype=torch.float)\n", "                    return item\n", "\n", "            if df_reg is None or \"label\" not in df_reg.columns or len(df_reg)==0:\n", "                raise RuntimeError(\"Sem dados de regressão; caindo para TF-IDF linear.\")\n", "\n", "            Xr = df_reg[\"text\"].astype(str).tolist()\n", "            yr = df_reg[\"label\"].astype(float).tolist()\n", "\n", "\n", "            tr_idx, te_idx = train_test_split(np.arange(len(Xr)), test_size=0.2, random_state=SEED)\n", "            ds_tr = TxtDS([Xr[i] for i in tr_idx], [yr[i] for i in tr_idx])\n", "            ds_te = TxtDS([Xr[i] for i in te_idx], [yr[i] for i in te_idx])\n", "\n", "            args = TrainingArguments(\n", "                output_dir=\"./out\", evaluation_strategy=\"epoch\",\n", "                per_device_train_batch_size=8, per_device_eval_batch_size=8,\n", "                learning_rate=2e-5, num_train_epochs=2, weight_decay=0.01,\n", "                logging_steps=50, save_strategy=\"no\"\n", "            )\n", "            def compute_metrics(eval_pred):\n", "                preds, labels = eval_pred\n", "                preds = preds.reshape(-1)\n", "                labels = labels.reshape(-1)\n", "                mae = np.mean(np.abs(preds - labels))\n", "                return {\"mae\": mae}\n", "\n", "            trainer = Trainer(model=model, args=args, train_dataset=ds_tr, eval_dataset=ds_te, compute_metrics=compute_metrics)\n", "            print(\"Treinando transformer...\")\n", "            t0 = time.time()\n", "            trainer.train()\n", "            print(f\"OK (transformer) em {time.time()-t0:.2f}s\")\n", "            models[\"transformer\"] = (tok, model)\n", "\n", "            def transformer_reg_predict(texts):\n", "                model.eval()\n", "                outs = []\n", "                with torch.no_grad():\n", "                    for t in texts:\n", "                        enc = tok(t, truncation=True, padding=\"max_length\", max_length=256, return_tensors=\"pt\").to(DEVICE)\n", "                        out = model(**enc).logits.squeeze().detach().cpu().item()\n", "                        outs.append(out)\n", "                return np.array(outs)\n", "            models[\"general_reg\"] = transformer_reg_predict\n", "            return models\n", "        except Exception as e:\n", "            print(\"Transformer indisponível ou falhou:\", e)\n", "            print(\"Fallback para TF-IDF linear.\")\n", "            return auto_train_model(df_clf, df_reg, {\"mode\":\"tfidf_linear\",\"task\":\"classification\"})"]}, {"cell_type": "markdown", "id": "ca4f8c9e", "metadata": {}, "source": ["### **07 -Explicabilidade (TF-IDF) + relatório de regex**\n", "\n", "explain_tfidf_linear_per_doc decompõe w·x para destacar n-grams com maior contribuição pró/contra (modelos lineares).\n", "Funciona quando o estimador possui coef_ (LogisticRegression/Ridge/LinearSVC calibrado).\n", "report_regex_matches mostra, por atendimento, quais critérios casaram (1) ou não (0).\n", "Use essas saídas para auditar decisões do sistema e orientar treinamentos e revisão de regex."]}, {"cell_type": "code", "execution_count": null, "id": "52b0b3b7", "metadata": {}, "outputs": [], "source": ["def explain_tfidf_linear_per_doc(pipeline_or_vec_clf, texts:list, top_k:int=10):\n", "\n", "    if isinstance(pipeline_or_vec_clf, Pipeline):\n", "        vec = pipeline_or_vec_clf.named_steps.get(\"vec\")\n", "        est = pipeline_or_vec_clf.named_steps.get(\"clf\", None) or pipeline_or_vec_clf.named_steps.get(\"reg\", None)\n", "    else:\n", "        raise ValueError(\"Forneça o Pipeline treinado com steps ('vec','clf'|'reg').\")\n", "\n", "    if not hasattr(est, \"coef_\"):\n", "        print(\"Explicação disponível apenas para modelos lineares com coef_.\")\n", "        return\n", "\n", "    feature_names = vec.get_feature_names_out()\n", "    X = vec.transform(texts)\n", "    coefs = est.coef_.reshape(-1)\n", "    contrib = X.multiply(coefs).toarray()\n", "\n", "    for i, txt in enumerate(texts):\n", "        row = contrib[i]\n", "        top_pos_idx = row.argsort()[-top_k:][::-1]\n", "        top_neg_idx = row.argsort()[:top_k]\n", "        print(f\"\\n[DOC {i}]\")\n", "        print(\"TOP +:\")\n", "        for j in top_pos_idx:\n", "            print(f\"  {feature_names[j]} -> {row[j]:.4f}\")\n", "        print(\"TOP -:\")\n", "        for j in top_neg_idx:\n", "            print(f\"  {feature_names[j]} -> {row[j]:.4f}\")\n", "\n", "def report_regex_matches(atendimento:dict, criteria:list):\n", "    texts = _by_speaker(atendimento)\n", "    m = regex_match_map(texts, criteria)\n", "    print(\"Regex matches (1=casou, 0=não):\")\n", "    for c in criteria:\n", "        print(f\"  {c['code']} {c['name']}: {m.get(c['code'],0)}\")\n"]}, {"cell_type": "markdown", "id": "d320a578", "metadata": {}, "source": ["### **08 -<PERSON><PERSON> <PERSON> entrada (sintéticos se necessário)**\n", "\n", "Carrega a variável global atendimentos; se ausente, gera 10 atendimentos sintéticos (5 positivos, 5 negativos).\n", "Os exemplos cobrem fatura, emergência/vazamento e religação, usando linguajar natural e marcas que acionam regex.\n", "Esses dados são úteis para validar o pipeline fim-a-fim e ajustar pesos/penalidades.\n", "Substitua pelos seus dados reais quando estiver pronto para treinar com volume maior."]}, {"cell_type": "code", "execution_count": null, "id": "baab01f6", "metadata": {}, "outputs": [], "source": ["if 'atendimentos' not in globals():\n", "    atendimentos = [\n", "\n", "        {\n", "          \"_id\":\"synth_pos_001\",\"canal\":\"0800\",\"tags\":[\"suporte\",\"fatura\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-08-30T10:00:00Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON> dia! Meu nome <PERSON>, <PERSON> Comgás. Como posso ajudar?\"},\n", "            {\"timestamp\":\"2025-08-30T10:00:10Z\",\"speaker\":\"user\",\"text\":\"Minha fatura veio acima do normal.\"},\n", "            {\"timestamp\":\"2025-08-30T10:00:25Z\",\"speaker\":\"agent\",\"text\":\"Entendo sua preocupação. Poderia confirmar seu nome completo e data de nascimento?\"},\n", "            {\"timestamp\":\"2025-08-30T10:00:40Z\",\"speaker\":\"user\",\"text\":\"<PERSON><PERSON>, nasci em 22/05/1990.\"},\n", "            {\"timestamp\":\"2025-08-30T10:01:05Z\",\"speaker\":\"agent\",\"text\":\"Verifiquei e identifiquei cobrança de média. Vou abrir OS de revisão; prazo de 2 dias úteis.\"},\n", "            {\"timestamp\":\"2025-08-30T10:01:25Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON><PERSON> claro? Posso abrir a solicitação?\"},\n", "            {\"timestamp\":\"2025-08-30T10:01:55Z\",\"speaker\":\"agent\",\"text\":\"Protocolo 555001. Mais alguma coisa? Responda à pesquisa de satisfação, por favor.\"}\n", "          ],\n", "          \"nota\": 9\n", "        },\n", "        {\n", "          \"_id\":\"synth_pos_002\",\"canal\":\"0800\",\"tags\":[\"emergência\",\"vazamento\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-08-30T11:10:00Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON><PERSON>, boa tarde! <PERSON>u nome <PERSON>, da Comgás.\"},\n", "            {\"timestamp\":\"2025-08-30T11:10:10Z\",\"speaker\":\"user\",\"text\":\"<PERSON><PERSON> de gás muito forte em casa.\"},\n", "            {\"timestamp\":\"2025-08-30T11:10:25Z\",\"speaker\":\"agent\",\"text\":\"<PERSON>bra portas e janelas, não acione interruptores e feche o registro.\"},\n", "            {\"timestamp\":\"2025-08-30T11:10:40Z\",\"speaker\":\"agent\",\"text\":\"Confirme seu nome completo e endereço, por favor.\"},\n", "            {\"timestamp\":\"2025-08-30T11:10:55Z\",\"speaker\":\"user\",\"text\":\"<PERSON><PERSON>, Rua Verde, 500, ap 12.\"},\n", "            {\"timestamp\":\"2025-08-30T11:11:15Z\",\"speaker\":\"agent\",\"text\":\"Abrirei a OS de emergência; chegada em 30 a 60 minutos.\"},\n", "            {\"timestamp\":\"2025-08-30T11:11:45Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON>u claro o procedimento? Posso ajudar em algo mais?\"},\n", "            {\"timestamp\":\"2025-08-30T11:12:00Z\",\"speaker\":\"agent\",\"text\":\"Participe da nossa pesquisa de satisfação ao encerrar.\"}\n", "          ],\n", "          \"nota\": 10\n", "        },\n", "        {\n", "          \"_id\":\"synth_pos_003\",\"canal\":\"0800\",\"tags\":[\"religacao\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-08-31T09:00:00Z\",\"speaker\":\"agent\",\"text\":\"O<PERSON>, bom dia! Meu nome <PERSON>, da Comgás.\"},\n", "            {\"timestamp\":\"2025-08-31T09:00:10Z\",\"speaker\":\"user\",\"text\":\"Pre<PERSON>o religar o gás.\"},\n", "            {\"timestamp\":\"2025-08-31T09:00:25Z\",\"speaker\":\"agent\",\"text\":\"Pode confirmar seu nome completo e data de nascimento?\"},\n", "            {\"timestamp\":\"2025-08-31T09:00:40Z\",\"speaker\":\"user\",\"text\":\"<PERSON><PERSON>, nasci em 12/07/1988.\"},\n", "            {\"timestamp\":\"2025-08-31T09:01:00Z\",\"speaker\":\"agent\",\"text\":\"A causa foi acesso ao medidor. Abrirei a solicitação; prazo 24 a 48 horas.\"},\n", "            {\"timestamp\":\"2025-08-31T09:01:20Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON><PERSON> claro? Mais alguma coisa?\"},\n", "            {\"timestamp\":\"2025-08-31T09:01:50Z\",\"speaker\":\"agent\",\"text\":\"Responda nossa pesquisa de satisfação, por favor.\"}\n", "          ],\n", "          \"nota\": 8\n", "        },\n", "        {\n", "          \"_id\":\"synth_pos_004\",\"canal\":\"0800\",\"tags\":[\"suporte\",\"fatura\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-08-31T14:30:00Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON> tarde! <PERSON><PERSON> a <PERSON>, da Comgás.\"},\n", "            {\"timestamp\":\"2025-08-31T14:30:12Z\",\"speaker\":\"user\",\"text\":\"Cobrança duplicada na fatura.\"},\n", "            {\"timestamp\":\"2025-08-31T14:30:28Z\",\"speaker\":\"agent\",\"text\":\"Sinto muito pelo incômodo. Confirme seu nome completo e data de nascimento.\"},\n", "            {\"timestamp\":\"2025-08-31T14:30:45Z\",\"speaker\":\"user\",\"text\":\"<PERSON><PERSON>, nasci em 10/10/1995.\"},\n", "            {\"timestamp\":\"2025-08-31T14:31:10Z\",\"speaker\":\"agent\",\"text\":\"Constatamos erro de leitura. Abrirei OS de revisão; prazo 2 dias úteis.\"},\n", "            {\"timestamp\":\"2025-08-31T14:31:25Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON><PERSON> claro? Posso abrir a solicitação?\"},\n", "            {\"timestamp\":\"2025-08-31T14:31:55Z\",\"speaker\":\"agent\",\"text\":\"Protocolo 777222. Mais algo? Participe da pesquisa de satisfação.\"}\n", "          ],\n", "          \"nota\": 7\n", "        },\n", "        {\n", "          \"_id\":\"synth_pos_005\",\"canal\":\"0800\",\"tags\":[\"suporte\",\"informacao\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-09-01T08:45:00Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON><PERSON>! Meu nome <PERSON>, da Comgás.\"},\n", "            {\"timestamp\":\"2025-09-01T08:45:12Z\",\"speaker\":\"user\",\"text\":\"Qual o prazo para uma visita técnica?\"},\n", "            {\"timestamp\":\"2025-09-01T08:45:25Z\",\"speaker\":\"agent\",\"text\":\"<PERSON>sso confirmar seu nome completo e data de nascimento?\"},\n", "            {\"timestamp\":\"2025-09-01T08:45:40Z\",\"speaker\":\"user\",\"text\":\"<PERSON><PERSON>, nasci em 01/01/1992.\"},\n", "            {\"timestamp\":\"2025-09-01T08:46:05Z\",\"speaker\":\"agent\",\"text\":\"Abrirei solicitação; prazo 1 a 2 dias úteis. Ficou claro?\"},\n", "            {\"timestamp\":\"2025-09-01T08:46:20Z\",\"speaker\":\"agent\",\"text\":\"Mais alguma coisa? Responda à pesquisa de satisfação ao encerrar.\"}\n", "          ],\n", "          \"nota\": 6\n", "        },\n", "\n", "        # 5 negativos (0..5)\n", "        {\n", "          \"_id\":\"synth_neg_001\",\"canal\":\"0800\",\"tags\":[\"suporte\",\"fatura\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-09-01T10:00:00Z\",\"speaker\":\"agent\",\"text\":\"Oi.\"},\n", "            {\"timestamp\":\"2025-09-01T10:00:12Z\",\"speaker\":\"user\",\"text\":\"Minha fatura veio alta.\"},\n", "            {\"timestamp\":\"2025-09-01T10:00:25Z\",\"speaker\":\"agent\",\"text\":\"O<PERSON>ha no site lá.\"}\n", "          ],\n", "          \"nota\": 3\n", "        },\n", "        {\n", "          \"_id\":\"synth_neg_002\",\"canal\":\"0800\",\"tags\":[\"emergência\",\"vazamento\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-09-01T11:20:00Z\",\"speaker\":\"agent\",\"text\":\"Qual é o problema?\"},\n", "            {\"timestamp\":\"2025-09-01T11:20:12Z\",\"speaker\":\"user\",\"text\":\"<PERSON><PERSON> de gás.\"},\n", "            {\"timestamp\":\"2025-09-01T11:20:25Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON> ser nada. Só abre a janela aí.\"}\n", "          ],\n", "          \"nota\": 2\n", "        },\n", "        {\n", "          \"_id\":\"synth_neg_003\",\"canal\":\"0800\",\"tags\":[\"religacao\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-09-01T12:05:00Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON>.\"},\n", "            {\"timestamp\":\"2025-09-01T12:05:12Z\",\"speaker\":\"user\",\"text\":\"<PERSON><PERSON> religar o gás.\"},\n", "            {\"timestamp\":\"2025-09-01T12:05:25Z\",\"speaker\":\"agent\",\"text\":\"Se<PERSON> prazo. Vê depois.\"}\n", "          ],\n", "          \"nota\": 1\n", "        },\n", "        {\n", "          \"_id\":\"synth_neg_004\",\"canal\":\"0800\",\"tags\":[\"suporte\",\"fatura\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-09-01T13:40:00Z\",\"speaker\":\"agent\",\"text\":\"Fala logo.\"},\n", "            {\"timestamp\":\"2025-09-01T13:40:12Z\",\"speaker\":\"user\",\"text\":\"Cobrança duplicada na fatura.\"},\n", "            {\"timestamp\":\"2025-09-01T13:40:25Z\",\"speaker\":\"agent\",\"text\":\"N<PERSON> tem o que fazer. Procura na internet.\"}\n", "          ],\n", "          \"nota\": 0\n", "        },\n", "        {\n", "          \"_id\":\"synth_neg_005\",\"canal\":\"0800\",\"tags\":[\"informacao\"],\n", "          \"messages\":[\n", "            {\"timestamp\":\"2025-09-01T15:15:00Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON>.\"},\n", "            {\"timestamp\":\"2025-09-01T15:15:12Z\",\"speaker\":\"user\",\"text\":\"Qual o prazo para atendimento?\"},\n", "            {\"timestamp\":\"2025-09-01T15:15:25Z\",\"speaker\":\"agent\",\"text\":\"<PERSON><PERSON> sei.\"}\n", "          ],\n", "          \"nota\": 4\n", "        }\n", "    ]\n", "\n", "print(f\"Total de atendimentos carregados: {len(atendimentos)}\")\n"]}, {"cell_type": "markdown", "id": "b0861242", "metadata": {}, "source": ["### **09 -Construção de df_reg e df_clf**\n", "\n", "Cria dois DataFrames: df_reg (alvo contínuo nota) e df_clf (alvo binário label ≥6 vs ≤5).\n", "Imprime shapes e amostra para validar que o parsing das estruturas está correto.\n", "Esses DataFrames alimentam o módulo de treino dinâmico e a avaliação subsequente.\n", "Se a coluna nota faltar, revise sua origem ou escolha apenas o caminho de classificação."]}, {"cell_type": "code", "execution_count": null, "id": "5d78370f", "metadata": {}, "outputs": [], "source": ["df_reg = build_df_from_atendimentos(atendimentos, task='regression')\n", "df_clf = build_df_from_atendimentos(atendimentos, task='classification')\n", "\n", "print(\"df_reg.shape:\", df_reg.shape, \"| df_clf.shape:\", df_clf.shape)\n", "print(df_reg.head(10))"]}, {"cell_type": "markdown", "id": "0e8348c6", "metadata": {}, "source": ["### **10 - <PERSON><PERSON><PERSON> (seleção dinâmica)**\n", "\n", "Seleciona a estratégia com auto_select_strategy e treina modelos via auto_train_model.\n", "Em TF-IDF linear, treinamos LogisticRegression (classificação) e Ridge (regressão de nota).\n", "Em TF-IDF+GBM, usamos RandomForest como robusto simples e Ridge para a nota.\n", "Em Transformer, usa BERTimbau; se indisponível, há fallback automático para TF-IDF."]}, {"cell_type": "code", "execution_count": null, "id": "6ff2d5a2", "metadata": {}, "outputs": [], "source": ["strategy = auto_select_strategy(len(df_reg))\n", "models = auto_train_model(df_clf, df_reg, strategy)"]}, {"cell_type": "markdown", "id": "6b54ef26", "metadata": {}, "source": ["### **11 - Avaliar 10 atendimentos (pipeline completo)**\n", "\n", "<PERSON><PERSON>, por atendimento: nota_model (ML), nota_regex (regras) e nota_final (híbrido com penalidade suave).\n", "model_only_score padroniza a obtenção da nota 0–10 via regressor ou probabilidade do classificador.\n", "Os tempos por atendimento ajudam a acompanhar latência e possíveis gargalos.\n", "Use esta impressão como sanity check antes de avançar para dados reais."]}, {"cell_type": "code", "execution_count": null, "id": "281ace1a", "metadata": {}, "outputs": [], "source": ["criteria = get_criteria(\"0800\")\n", "\n", "def model_only_score(a, models):\n", "\n", "    txt = concat_messages(a)\n", "    if models.get(\"general_reg\") is not None:\n", "        reg = models[\"general_reg\"]\n", "        try:\n", "            if callable(reg):\n", "                y = float(reg([txt])[0])\n", "            else:\n", "                y = float(reg.predict([txt])[0])\n", "            return float(max(0.0, min(10.0, y)))\n", "        except Exception:\n", "            pass\n", "    if models.get(\"general_clf\") is not None:\n", "        clf = models[\"general_clf\"]\n", "        try:\n", "            proba = clf.predict_proba([txt])[0]\n", "            p = float(proba[1]) if len(proba)==2 else float(max(proba))\n", "            return round(10.0 * p, 2)\n", "        except Exception:\n", "            return 5.0\n", "    return 5.0\n", "\n", "for i, a in enumerate(atendimentos[:10], 1):\n", "    t0 = time.time()\n", "    texts = _by_speaker(a)\n", "    nota_regex = compute_rule_only_score(texts, criteria)\n", "    nota_model = model_only_score(a, models)\n", "    nota_final = evaluate_transcription(a, criteria, models=models, alpha_ml=0.6, penalty_must=0.20, debug=False)\n", "    print(f\"{i:02d} | {a['_id']} | nota_informada={a.get('nota','NA')} | nota_model={nota_model:.2f} | nota_regex={nota_regex:.2f} | nota_final={nota_final:.2f} | t={time.time()-t0:.2f}s\")"]}, {"cell_type": "markdown", "id": "0fc38912", "metadata": {}, "source": ["### **12 -Explicabilidade e regex (2 exemplos)**\n", "\n", "Para dois atendimentos, imprime os critérios casados e os top n-grams mais influentes (se TF-IDF linear estiver ativo).\n", "Isso evidencia por que a nota foi atribuída, apoiando auditoria e evolução das regras.\n", "Se o estimador não tiver coef_ (ex.: RandomForest), a explicação de n-grams não será exibida.\n", "Você pode alterar top_k para aprofundar a análise."]}, {"cell_type": "code", "execution_count": null, "id": "afe73f5b", "metadata": {}, "outputs": [], "source": ["print(\"\\n=== EXPLICABILIDADE (2 exemplos) ===\")\n", "sample_idxs = [0, min(1, len(atendimentos)-1)]\n", "for idx in sample_idxs:\n", "    a = atendimentos[idx]\n", "    print(f\"\\n--- Atendimento {_id if (_id:=a.get('_id')) else idx} ---\")\n", "    report_regex_matches(a, criteria)\n", "    if models.get(\"general_clf\") is not None and isinstance(models[\"general_clf\"], Pipeline):\n", "        try:\n", "            explain_tfidf_linear_per_doc(models[\"general_clf\"], [concat_messages(a)], top_k=8)\n", "        except Exception as e:\n", "            print(\"Explicação TF-IDF indisponível:\", e)"]}, {"cell_type": "markdown", "id": "c80789f5", "metadata": {}, "source": ["### **13 - Função de previsão para novo atendimento**\n", "\n", "prever_nota_atendimento é a interface única para pontuar um atendimento sem nota.\n", "Ela invoca evaluate_transcription com os critérios do canal (default 0800) e os modelos treinados.\n", "A saída é uma nota contínua 0–10, já com penalidade suave por MUSTs não atendidos."]}, {"cell_type": "code", "execution_count": null, "id": "67fc9e6e", "metadata": {}, "outputs": [], "source": ["def prever_nota_atendimento(novo_atendimento:dict, channel=\"0800\", models=None, alpha_ml=0.6, penalty_must=0.20):\n", "    crit = get_criteria(channel)\n", "    nota = evaluate_transcription(novo_atendimento, crit, models=models, alpha_ml=alpha_ml, penalty_must=penalty_must, debug=False)\n", "    return nota"]}, {"cell_type": "code", "execution_count": null, "id": "67539de1", "metadata": {}, "outputs": [], "source": ["novo_atendimento = {\n", "    \"_id\": \"externo_religacao_001\",\n", "    \"canal\": \"0800\",\n", "    \"tags\": [\"suporte\", \"religacao\"],\n", "    \"messages\": [\n", "        {\"timestamp\": \"2025-09-12T13:15:00Z\", \"speaker\": \"agent\", \"text\": \"<PERSON><PERSON><PERSON>, bom dia! Meu nome <PERSON>, da Comgás. Como posso ajudar?\"},\n", "        {\"timestamp\": \"2025-09-12T13:15:06Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON> <PERSON><PERSON>, preciso solicitar a religação do gás do meu apartamento.\"},\n", "        {\"timestamp\": \"2025-09-12T13:15:14Z\", \"speaker\": \"agent\", \"text\": \"Entendo sua necessidade, vamos resolver. <PERSON> seguir, poderia confirmar seus dados, por favor? Preciso do nome completo e da data de nascimento.\"},\n", "        {\"timestamp\": \"2025-09-12T13:15:26Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON>, nasci em 15/07/1987.\"},\n", "        {\"timestamp\": \"2025-09-12T13:15:35Z\", \"speaker\": \"agent\", \"text\": \"<PERSON><PERSON><PERSON><PERSON>, <PERSON>. Para localizar o contrato, você pode informar o endereço completo do imóvel?\"},\n", "        {\"timestamp\": \"2025-09-12T13:15:47Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON> das Palmeiras, 200, ap 501, <PERSON><PERSON>, São Paulo.\"},\n", "        {\"timestamp\": \"2025-09-12T13:15:58Z\", \"speaker\": \"agent\", \"text\": \"Perfeito. Verifiquei aqui e identifiquei que a suspensão ocorreu por falta de acesso ao medidor na última visita.\"},\n", "        {\"timestamp\": \"2025-09-12T13:16:10Z\", \"speaker\": \"agent\", \"text\": \"<PERSON><PERSON><PERSON><PERSON> o transtorno. Para a religação, precisamos garantir que o local do medidor esteja desobstruído. Está tudo acessível agora?\"},\n", "        {\"timestamp\": \"2025-09-12T13:16:21Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON>, j<PERSON> deixei o acesso livre. O porteiro está orientado também.\"},\n", "        {\"timestamp\": \"2025-09-12T13:16:30Z\", \"speaker\": \"agent\", \"text\": \"<PERSON><PERSON><PERSON>, obrigada pela confirmação. Vou abrir uma OS de religação e registrar o agendamento.\"},\n", "        {\"timestamp\": \"2025-09-12T13:16:42Z\", \"speaker\": \"agent\", \"text\": \"<PERSON><PERSON>, só checando: há algum cheiro de gás no ambiente agora? Alguma chama acesa ou equipamento ligado próximo ao medidor?\"},\n", "        {\"timestamp\": \"2025-09-12T13:16:52Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON>, está tudo seguro. Sem cheiro e nenhum equipamento ligado.\"},\n", "        {\"timestamp\": \"2025-09-12T13:17:01Z\", \"speaker\": \"agent\", \"text\": \"Perfeito. O procedimento padrão é a abertura da solicitação e o técnico faz a religação no local.\"},\n", "        {\"timestamp\": \"2025-09-12T13:17:12Z\", \"speaker\": \"agent\", \"text\": \"O prazo de atendimento é entre 24 e 48 horas, normalmente em dia útil. Também posso buscar uma janela para amanhã de man<PERSON>ã.\"},\n", "        {\"timestamp\": \"2025-09-12T13:17:23Z\", \"speaker\": \"user\",  \"text\": \"Amanh<PERSON> de manhã seria ótimo. Qual o horário?\"},\n", "        {\"timestamp\": \"2025-09-12T13:17:32Z\", \"speaker\": \"agent\", \"text\": \"<PERSON><PERSON> janela entre 08:00 e 12:00. <PERSON><PERSON> tentar priorizar até 10:00, mas depende da rota do técnico.\"},\n", "        {\"timestamp\": \"2025-09-12T13:17:43Z\", \"speaker\": \"user\",  \"text\": \"Sem problemas, pode ser entre 8 e 12.\"},\n", "        {\"timestamp\": \"2025-09-12T13:17:51Z\", \"speaker\": \"agent\", \"text\": \"Certo. Ficou claro o procedimento? Posso abrir a solicitação agora?\"},\n", "        {\"timestamp\": \"2025-09-12T13:17:58Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON><PERSON> claro, pode abrir sim.\"},\n", "        {\"timestamp\": \"2025-09-12T13:18:08Z\", \"speaker\": \"agent\", \"text\": \"Solicitação aberta: protocolo 846391. <PERSON><PERSON> incluir as observações sobre acesso liberado e porteiro orientado.\"},\n", "        {\"timestamp\": \"2025-09-12T13:18:19Z\", \"speaker\": \"agent\", \"text\": \"Reforçando: mantenha a área do medidor desobstruída e, no momento da visita, tenha um adulto disponível para acompanhar o técnico.\"},\n", "        {\"timestamp\": \"2025-09-12T13:18:29Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON> deixa<PERSON>, estarei em casa amanhã de man<PERSON>ã.\"},\n", "        {\"timestamp\": \"2025-09-12T13:18:36Z\", \"speaker\": \"agent\", \"text\": \"Excelente. Caso o técnico identifique alguma irregularidade interna, ele vai orientar os próximos passos. Mas pela sua descrição, deve ser apenas religação.\"},\n", "        {\"timestamp\": \"2025-09-12T13:18:48Z\", \"speaker\": \"user\",  \"text\": \"Entendi. Vocês avisam quando o técnico estiver chegando?\"},\n", "        {\"timestamp\": \"2025-09-12T13:18:56Z\", \"speaker\": \"agent\", \"text\": \"<PERSON><PERSON>, você receberá um SMS de aviso. Se preferir, posso registrar telefone alternativo.\"},\n", "        {\"timestamp\": \"2025-09-12T13:19:04Z\", \"speaker\": \"user\",  \"text\": \"Pode usar este mesmo número da ligação, por favor.\"},\n", "        {\"timestamp\": \"2025-09-12T13:19:12Z\", \"speaker\": \"agent\", \"text\": \"<PERSON><PERSON><PERSON><PERSON> <PERSON>, mais alguma dúvida sobre o processo ou sobre prazos?\"},\n", "        {\"timestamp\": \"2025-09-12T13:19:19Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON> confirmando: então o prazo é entre 24 e 48 horas, mas tentam ir amanhã de manhã dentro da janela.\"},\n", "        {\"timestamp\": \"2025-09-12T13:19:30Z\", \"speaker\": \"agent\", \"text\": \"Exatamente. O compromisso é de 24 a 48 horas; com a janela de amanhã das 08:00 às 12:00.\"},\n", "        {\"timestamp\": \"2025-09-12T13:19:39Z\", \"speaker\": \"agent\", \"text\": \"Mais alguma coisa em que eu possa ajudar?\"},\n", "        {\"timestamp\": \"2025-09-12T13:19:46Z\", \"speaker\": \"user\",  \"text\": \"<PERSON><PERSON>, é isso mesmo. Obrigado pelo atendimento.\"},\n", "        {\"timestamp\": \"2025-09-12T13:19:53Z\", \"speaker\": \"agent\", \"text\": \"<PERSON>u que agradeço. Se possível, ao encerrar a chamada, participe da nossa pesquisa de satisfação. Tenha um ótimo dia!\"}\n", "    ]\n", "}\n", "\n", "\n", "nota_prevista = prever_nota_atendimento(novo_atendimento, channel=\"0800\", models=models, alpha_ml=0.6, penalty_must=0.20)\n", "print(\"Nota prevista (0–10):\", round(nota_prevista,2))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}