"""
Roteador de healthcheck (MVP).

Fornece o endpoint GET /health com:
- status geral (ok|degraded)
- verificação opcional do MongoDB via ping
- versão curta (quando disponível em env)

O objetivo é oferecer uma checagem rápida e estável para desenvolvimento.
"""

import os
from typing import Dict

from fastapi import APIRouter

try:  # pymongo é opcional; o health funciona mesmo sem ele
    from pymongo import MongoClient
    from pymongo.errors import PyMongoError
except Exception:  # pragma: no cover - ambiente sem pymongo
    MongoClient = None  # type: ignore
    PyMongoError = Exception  # type: ignore


router = APIRouter()


def _check_mongo() -> Dict[str, str]:
    """Realiza ping ao MongoDB se MONGO_URI estiver configurado.

    Retorna dict com chaves: status (ok|down|skipped) e, quando down, um erro reduzido.
    """
    mongo_uri = os.getenv("MONGO_URI")
    if not mongo_uri or MongoClient is None:
        return {"status": "skipped"}

    try:
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=300)
        client.admin.command("ping")
        return {"status": "ok"}
    except PyMongoError as exc:  # pragma: no cover - caminho dependente de ambiente
        return {"status": "down", "error": str(exc)[:160]}


@router.get("/health")
def health():
    """Retorna saúde básica do serviço com checks mínimos.

    Sempre responde 200 em dev. Em produção, a política pode mudar conforme necessidade.
    """
    mongo = _check_mongo()
    overall = "ok" if mongo.get("status") in {"ok", "skipped"} else "degraded"
    version = os.getenv("APP_VERSION", "dev")
    return {"status": overall, "mongo": mongo, "version": version}


