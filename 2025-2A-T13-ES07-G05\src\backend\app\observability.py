"""
Módu<PERSON> de Observabilidade (MVP) para a aplicação FastAPI.

Este módulo fornece:
- Configuração de logs estruturados em JSON
- Middleware para registrar requisições HTTP com correlação e duração
- Exposição de métricas Prometheus via Instrumentator

Mantém-se o design mínimo e não intrusivo: uma função de setup que pode ser
invocada a partir do main sem alterar rotas existentes.
"""

import logging
import os
import time
import uuid
from typing import Optional

from fastapi import FastAPI, Request
from pythonjsonlogger import jsonlogger
from prometheus_fastapi_instrumentator import Instrumentator


def _configure_json_logging() -> logging.Logger:
    """Configura logging estruturado em JSON no stdout.

    Campos principais registrados por padrão:
    - asctime, levelname, message
    - event (quando passado em dict), correlationId, method, path, status, durationMs

    Retorna um logger de aplicação pronto para uso.
    """
    logger = logging.getLogger("app")
    logger.setLevel(logging.INFO)

    # Evita duplicação de handlers em reloads de dev
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = jsonlogger.JsonFormatter(
            fmt="%(asctime)s %(levelname)s %(message)s"
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    return logger


def _install_request_logging_middleware(app: FastAPI, logger: logging.Logger) -> None:
    """Adiciona middleware que registra cada requisição processada pela app.

    O log inclui correlação, caminho, método, status e duração.
    """

    @app.middleware("http")
    async def _log_requests(request: Request, call_next):  # type: ignore[misc]
        start = time.time()
        correlation_id = request.headers.get("X-Request-Id", str(uuid.uuid4()))

        try:
            response = await call_next(request)
            duration_ms = int((time.time() - start) * 1000)
            logger.info(
                {
                    "event": "request",
                    "method": request.method,
                    "path": request.url.path,
                    "status": response.status_code,
                    "durationMs": duration_ms,
                    "correlationId": correlation_id,
                    "clientIp": request.client.host if request.client else None,
                    "userAgent": request.headers.get("user-agent"),
                }
            )
            response.headers["X-Request-Id"] = correlation_id
            return response
        except Exception as exc:  # pragma: no cover - caminho de erro genérico
            duration_ms = int((time.time() - start) * 1000)
            logger.error(
                {
                    "event": "error",
                    "method": request.method,
                    "path": request.url.path,
                    "durationMs": duration_ms,
                    "correlationId": correlation_id,
                    "error": str(exc),
                }
            )
            raise


def _expose_metrics(app: FastAPI) -> None:
    """Expõe métricas Prometheus no endpoint /metrics utilizando o Instrumentator.

    O Instrumentator inclui métricas HTTP básicas (contagem, duração, tamanho) sem
    alterar rotas existentes.
    """
    Instrumentator().instrument(app).expose(app, endpoint="/metrics")


def setup_observability(app: FastAPI, *, enabled: Optional[bool] = None) -> None:
    """Inicializa a observabilidade de forma segura e idempotente.

    - enabled: se None, lê OBSERVABILITY_ENABLED (default: true em dev).
    - Configura logs JSON, middleware de request e expõe /metrics.
    """
    if enabled is None:
        enabled = os.getenv("OBSERVABILITY_ENABLED", "true").lower() in {"1", "true", "yes"}
    if not enabled:
        return

    logger = _configure_json_logging()
    _install_request_logging_middleware(app, logger)
    _expose_metrics(app)


