import React, { useMemo, useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, LineChart, Line, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import { Search, ChevronRight, ChevronLeft, Play, Pause, FileText, LayoutDashboard, ListChecks, ArrowLeft, Filter, Download, PhoneCall, Circle, Clock, Tag, TrendingUp, Info, Timer, Target, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

/**
 * Comgás – Front-end React Dashboard
 * ------------------------------------------------------
 * - Three screens: Dashboard, Transcrições, Detalhes.
 * - State-driven navigation (no router needed for preview).
 * - Mocked charts & transcripts with clear integration points.
 * - Light theme design faithful to mockups.
 * - Note: We do NOT store raw audio (mp3). Only transcripts + metadata.
 */

// --- Mock types (easy to replace with backend DTOs) ------------------------
export type Transcript = {
  id: string;
  protocolo: string;
  cliente: string;
  telefone: string;
  criadoEm: string; // ISO datetime
  duracaoSeg: number;
  sentimento: "positivo" | "neutro" | "negativo";
  resumo: string;
  tags: string[];
  scoreQualidade: number; // 0..100
  canal: "voz" | "chat";
  agente: string;
  // Observação importante: não armazenamos áudio bruto (mp3). Somente texto + metadados.
  transcricao: string[]; // turnos (alternância)
};

// --- Mock Service Layer (swap with real API) -------------------------------
const Api = {
  async listarTranscricoes(params?: {
    page?: number; pageSize?: number; query?: string; sentimento?: string[]; agente?: string;
    duracaoMin?: number; duracaoMax?: number; qualidadeMin?: number; qualidadeMax?: number;
  }): Promise<{ items: Transcript[]; total: number; }>{
    // Simula paginação/filtragem — substitua por fetch('/api/transcricoes?...')
    const db = MOCK_DATA;
    let filtered = db;
    if (params?.query) {
      const q = params.query.toLowerCase();
      filtered = filtered.filter(t =>
        t.protocolo.toLowerCase().includes(q) ||
        t.cliente.toLowerCase().includes(q) ||
        t.resumo.toLowerCase().includes(q)
      );
    }
    if (params?.sentimento && params.sentimento.length) {
      filtered = filtered.filter(t => params.sentimento!.includes(t.sentimento));
    }
    if (params?.agente) {
      filtered = filtered.filter(t => t.agente === params.agente);
    }
    if (params?.duracaoMin !== undefined) {
      filtered = filtered.filter(t => t.duracaoSeg >= params.duracaoMin!);
    }
    if (params?.duracaoMax !== undefined) {
      filtered = filtered.filter(t => t.duracaoSeg <= params.duracaoMax!);
    }
    if (params?.qualidadeMin !== undefined) {
      filtered = filtered.filter(t => t.scoreQualidade >= params.qualidadeMin!);
    }
    if (params?.qualidadeMax !== undefined) {
      filtered = filtered.filter(t => t.scoreQualidade <= params.qualidadeMax!);
    }
    const page = params?.page ?? 1;
    const pageSize = params?.pageSize ?? 8;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    return new Promise(r => setTimeout(() => r({ items: filtered.slice(start, end), total: filtered.length }), 250));
  },
  async obterTranscricao(id: string): Promise<Transcript | undefined> {
    // Substituir por fetch(`/api/transcricoes/${id}`)
    return new Promise(r => setTimeout(() => r(MOCK_DATA.find(x => x.id === id)), 200));
  },
  async obterKpis() {
    // Substituir por fetch('/api/dashboard/kpis')
    return new Promise<{kpis: {label:string, value:string, delta?:string, hint?:string}[]; }>(r => 
      setTimeout(() => r({
        kpis: [
          { label: "Avaliação 0", value: "2", hint: "Péssima" },
          { label: "Avaliação 1", value: "8", hint: "Ruim" },
          { label: "Avaliação 2", value: "15", hint: "Regular" },
          { label: "Avaliação 3", value: "28", hint: "Boa" },
          { label: "Avaliação 4", value: "35", hint: "Muito Boa" },
          { label: "Avaliação 5", value: "42", hint: "Excelente" },
        ],
      }), 220)
    );
  },
  async obterSeries() {
    // Substituir por fetch('/api/dashboard/series')
    return new Promise(r => setTimeout(() => r({
      byMonth: Array.from({length: 12}).map((_,i)=>({ 
        mes: `${i+1}/24`, 
        positivo: Math.round(80+Math.random()*40),
        neutro: Math.round(60+Math.random()*30),
        negativo: Math.round(20+Math.random()*25)
      })),
      sentiment: [
        { name: "Positivo", value: 46, color: "#22C55E" },
        { name: "Neutro", value: 38, color: "#F59E0B" },
        { name: "Negativo", value: 16, color: "#EF4444" },
      ],
      byWeek: Array.from({length: 8}).map((_,i)=>({ semana: `S${i+1}`, chamadas: Math.round(150+Math.random()*100) })),
      avaliacoes: [
        { rating: "Péssima (1)", percent: 3.2 },
        { rating: "Ruim (2)", percent: 8.7 },
        { rating: "Regular (3)", percent: 22.1 },
        { rating: "Boa (4)", percent: 35.8 },
        { rating: "Excelente (5)", percent: 30.2 },
      ]
    }), 220));
  }
};

// --- App Navigation --------------------------------------------------------

type Screen = { name: "dashboard" } | { name: "transcricoes" } | { name: "detalhes"; id: string };

export default function ComgasApp() {
  const [screen, setScreen] = useState<Screen>({ name: "dashboard" });
  const goto = (s: Screen) => setScreen(s);

  return (
    <div className="min-h-screen flex bg-background">
      <Sidebar onNavigate={goto} active={screen.name} />
      <main className="flex-1 p-3 md:p-4 lg:p-6 xl:p-8 2xl:p-10 space-y-4 md:space-y-6 max-w-full overflow-hidden">
        <Header onNavigate={goto} />
        {screen.name === "dashboard" && <Dashboard onOpenTransc={() => goto({ name: "transcricoes" })} />}
        {screen.name === "transcricoes" && <Transcricoes onOpenDetalhes={(id)=>goto({ name: "detalhes", id })} />}
        {screen.name === "detalhes" && <Detalhes id={screen.id} onBack={()=>goto({ name: "transcricoes" })} />}
      </main>
    </div>
  );
}

// --- Sidebar ---------------------------------------------------------------
function Sidebar({ onNavigate, active }:{ onNavigate:(s:Screen)=>void; active:string }){
  const Item = ({ id, icon:Icon, label }:{ id:Screen["name"]; icon:any; label:string }) => (
    <Button
      variant={active === id ? "default" : "ghost"}
      className="w-full justify-start gap-3 mb-1"
      onClick={()=>onNavigate({ name:id } as Screen)}
    >
      <Icon size={18} />
      <span className="font-medium">{label}</span>
    </Button>
  );
  
  return (
    <aside className="hidden md:flex flex-col w-56 lg:w-64 xl:w-72 p-3 md:p-4 bg-sidebar border-r border-sidebar-border">
      <div className="flex items-center gap-2 mb-6 p-2">
        <div className="h-10 w-10 rounded-xl bg-primary grid place-items-center font-bold text-primary-foreground">
          C
        </div>
        <div>
          <div className="font-semibold text-foreground">Comgás QA Voz</div>
          <div className="text-xs text-muted-foreground">Transcrição & Análise</div>
        </div>
      </div>
      
      <nav className="space-y-1">
        <Item id="dashboard" icon={LayoutDashboard} label="Dashboard" />
        <Item id="transcricoes" icon={ListChecks} label="Transcrições" />
      </nav>
      
      <div className="mt-auto text-xs text-muted-foreground p-2">
        <p className="mb-1 font-medium">Política de dados</p>
        <p>Não armazenamos áudio bruto (mp3); apenas transcrições e metadados.</p>
      </div>
    </aside>
  );
}

// --- Header ----------------------------------------------------------------
function Header({ onNavigate }:{ onNavigate:(s:Screen)=>void }){
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 className="text-2xl md:text-3xl font-semibold text-foreground">
          Monitoramento de Atendimento
        </h1>
        <p className="text-muted-foreground">
          Insights operacionais a partir de transcrições (Whisper) e NLP.
        </p>
      </div>
      <div className="flex items-center gap-3">
       <Input
          type="file"
          accept="audio/mpeg,audio/wav"
          className="hidden"
          id="file-upload"
          onChange={async (e) => {
            const file = e.target.files?.[0];
            if (!file) return;
            
            if (!['audio/mpeg', 'audio/wav'].includes(file.type)) {
              alert('Apenas arquivos MP3 ou WAV são permitidos.');
              return;
            }
            
            const formData = new FormData();
            formData.append("audio_file", file);
            
            try {
              console.log("Iniciando transcrição...");
              
              const response = await fetch("http://127.0.0.1:8000/stt/transcribe", {
                method: "POST",
                body: formData,
              });
              
              if (response.ok) {
                const result = await response.json();
                console.log("Transcrição realizada:", result);
                
                alert(`Transcrição concluída!\n\nTexto: ${result.texto}\n\nDuração: ${result.duracao_segundos}s\nConfiança: ${result.confianca_media?.toFixed(2)}`);
                
                // A fazer: recarregar a lista de transcrições se estivermos na tela de transcrições
                // window.location.reload();
                
              } else {
                const error = await response.json();
                console.error("Erro na transcrição:", error);
                alert(`Erro: ${error.detail.message || 'Falha na transcrição'}`);
              }
            } catch (err) {
              console.error("Erro de rede:", err);
              alert("Erro de conexão. Verifique se o backend está rodando.");
            }
          }}
        />
        <Label htmlFor="file-upload" className="cursor-pointer">
          <Button
            variant="default"
            size="default"
            type="button"
            asChild
          >
            <span>Adicionar chamada</span>
          </Button>
        </Label>
        <Button variant="outline" size="default" onClick={()=>onNavigate({ name: "transcricoes" })}>
          <FileText size={16} className="mr-2"/>
          Ver transcrições
        </Button>
        <Button variant="default" size="default" onClick={()=>onNavigate({ name: "dashboard" })}>
          <LayoutDashboard size={16} className="mr-2"/>
          Dashboard
        </Button>
      </div>
    </div>
  );
}

// --- Dashboard -------------------------------------------------------------
function Dashboard({ onOpenTransc }:{ onOpenTransc:()=>void }){
  const [kpis, setKpis] = useState<{label:string, value:string, delta?:string, hint?:string}[]>([]);
  const [series, setSeries] = useState<any>(null);

  React.useEffect(()=>{ 
    Api.obterKpis().then(r=>setKpis(r.kpis)); 
    Api.obterSeries().then(setSeries); 
  },[]);

  return (
    <div className="space-y-4 md:space-y-6">
      {/* KPI Cards - Avaliações */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 md:gap-4">
        {kpis.map((k, idx)=> (
          <motion.div 
            key={idx} 
            className="bg-card border border-border rounded-xl p-3 md:p-4 shadow-soft"
            initial={{opacity:0, y:20}} 
            animate={{opacity:1, y:0}}
            transition={{delay: idx * 0.1}}
          >
            <div className="text-xs md:text-sm text-muted-foreground mb-1">{k.label}</div>
            <div className="text-lg md:text-xl lg:text-2xl font-semibold text-foreground mb-2">{k.value}</div>
            <div className="flex items-center gap-2 text-xs">
              {k.hint && <span className="text-muted-foreground">{k.hint}</span>}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Monthly Calls Line Chart */}
        <Card className="p-6 shadow-soft">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-foreground">Chamadas por Mês</h3>
            <span className="text-xs text-muted-foreground">Evolução Mensal</span>
          </div>
          <div className="h-64">
            {series && (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={series.byMonth}>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                  <XAxis 
                    dataKey="mes" 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: "hsl(var(--card))", 
                      border: "1px solid hsl(var(--border))", 
                      borderRadius: "8px",
                      color: "hsl(var(--foreground))",
                      fontSize: "12px"
                    }} 
                  />
                  <Line type="monotone" dataKey="positivo" stroke="#22C55E" strokeWidth={2} name="Positivo" />
                  <Line type="monotone" dataKey="neutro" stroke="#F59E0B" strokeWidth={2} name="Neutro" />
                  <Line type="monotone" dataKey="negativo" stroke="#EF4444" strokeWidth={2} name="Negativo" />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </Card>

        {/* Sentiment Pie Chart with Legend */}
        <Card className="p-6 shadow-soft">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-foreground">Distribuição por Sentimento</h3>
            <span className="text-xs text-muted-foreground">Últimos 30 dias</span>
          </div>
          <div className="h-64 flex items-center">
            {series && (
              <div className="w-full flex items-center">
                <ResponsiveContainer width="60%" height={200}>
                  <PieChart>
                    <Pie 
                      data={series.sentiment} 
                      dataKey="value" 
                      nameKey="name" 
                      innerRadius={40} 
                      outerRadius={80}
                    >
                      {series.sentiment.map((entry:any, i:number)=> (
                        <Cell key={i} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: "hsl(var(--card))", 
                        border: "1px solid hsl(var(--border))", 
                        borderRadius: "8px",
                        color: "hsl(var(--foreground))"
                      }} 
                    />
                  </PieChart>
                </ResponsiveContainer>
                <div className="ml-4 space-y-2">
                  {series.sentiment.map((item:any, i:number) => (
                    <div key={i} className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full" style={{backgroundColor: item.color}}></div>
                      <span className="text-sm">{item.name}: {item.value}%</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Weekly Calls and Rating Distribution */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Weekly Calls Bar Chart */}
        <Card className="p-6 shadow-soft">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-foreground">Chamadas por Semana</h3>
            <span className="text-xs text-muted-foreground">Volume Semanal</span>
          </div>
          <div className="h-64">
            {series && (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={series.byWeek}>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                  <XAxis 
                    dataKey="semana" 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: "hsl(var(--card))", 
                      border: "1px solid hsl(var(--border))", 
                      borderRadius: "8px",
                      color: "hsl(var(--foreground))",
                      fontSize: "12px"
                    }} 
                  />
                  <Bar 
                    dataKey="chamadas" 
                    radius={[6,6,0,0]} 
                    fill="hsl(var(--primary))" 
                  />
                </BarChart>
              </ResponsiveContainer>
            )}
          </div>
        </Card>

        {/* Rating Distribution Table */}
        <Card className="p-6 shadow-soft">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-foreground">Ranking de Avaliações</h3>
            <span className="text-xs text-muted-foreground">Distribuição (%)</span>
          </div>
          <div className="h-64 flex items-center">
            {series && (
              <div className="w-full space-y-4">
                {series.avaliacoes.map((item:any, i:number) => (
                  <div key={i} className="flex items-center justify-between">
                    <span className="text-sm font-medium min-w-[100px]">{item.rating}</span>
                    <div className="flex-1 mx-3">
                      <div className="w-full bg-muted h-3 rounded-full">
                        <div 
                          className="h-3 rounded-full transition-all duration-500" 
                          style={{
                            width: `${item.percent}%`,
                            backgroundColor: i === 0 ? '#EF4444' : i === 1 ? '#F97316' : i === 2 ? '#F59E0B' : i === 3 ? '#84CC16' : '#22C55E'
                          }}
                        />
                      </div>
                    </div>
                    <span className="text-sm font-mono min-w-[50px] text-right">{item.percent}%</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Action Button */}
      <div className="flex justify-end">
        <Button onClick={onOpenTransc} size="lg">
          Ver transcrições
          <ChevronRight size={16} className="ml-2"/>
        </Button>
      </div>
    </div>
  );
}

// --- Transcrições ----------------------------------------------------------
function Transcricoes({ onOpenDetalhes }:{ onOpenDetalhes:(id:string)=>void }){
  const [query, setQuery] = useState("");
  const [sentFilter, setSentFilter] = useState<string[]>([]);
  const [durationFilter, setDurationFilter] = useState([0, 300]); // 0-5 minutes in seconds
  const [qualityFilter, setQualityFilter] = useState([0, 100]); // 0-100%
  const [page, setPage] = useState(1);
  const pageSize = 4; // Changed to 4 items per page
  const [data, setData] = useState<{items:Transcript[]; total:number}>({ items: [], total: 0 });
  const [isUploading, setIsUploading] = useState(false);

  const reload = React.useCallback(()=>{
    Api.listarTranscricoes({ 
      page, 
      pageSize, 
      query, 
      sentimento: sentFilter,
      duracaoMin: durationFilter[0],
      duracaoMax: durationFilter[1],
      qualidadeMin: qualityFilter[0],
      qualidadeMax: qualityFilter[1]
    }).then(setData);
  },[page, pageSize, query, sentFilter, durationFilter, qualityFilter]);

  React.useEffect(()=>{ reload(); }, [reload]);

  const toggleSent = (s:string)=> setSentFilter(prev => prev.includes(s) ? prev.filter(x=>x!==s) : [...prev, s]);

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-3 md:gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl md:text-2xl lg:text-3xl font-semibold text-foreground mb-2">Transcrições</h2>
          <p className="text-muted-foreground text-sm md:text-base">
            Pesquisa, filtros e acesso ao detalhe. Não armazenamos áudio bruto; apenas texto e metadados.
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"/>
            <Input
              value={query}
              onChange={e=>{ setPage(1); setQuery(e.target.value); }}
              placeholder="Buscar por protocolo, cliente, resumo..."
              className="pl-10 w-[280px]"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="default">
                <Filter size={16} className="mr-2"/>
                Filtros
                <ChevronDown size={16} className="ml-2"/>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-80 bg-card border border-border shadow-medium z-50 p-4">
              <DropdownMenuLabel className="text-foreground mb-3">Filtrar por:</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-border mb-4"/>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" className="w-full justify-start mb-2">
                    <Timer size={16} className="mr-2 text-muted-foreground"/>
                    <span className="text-foreground">Por duração</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 bg-card border border-border shadow-medium z-50">
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Duração: {Math.floor(durationFilter[0]/60)}m {durationFilter[0]%60}s - {Math.floor(durationFilter[1]/60)}m {durationFilter[1]%60}s</Label>
                    <Slider
                      value={durationFilter}
                      onValueChange={setDurationFilter}
                      max={600}
                      min={0}
                      step={30}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0s</span>
                      <span>10m</span>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" className="w-full justify-start">
                    <Target size={16} className="mr-2 text-muted-foreground"/>
                    <span className="text-foreground">Por qualidade</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 bg-card border border-border shadow-medium z-50">
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Qualidade: {qualityFilter[0]}% - {qualityFilter[1]}%</Label>
                    <Slider
                      value={qualityFilter}
                      onValueChange={setQualityFilter}
                      max={100}
                      min={0}
                      step={5}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button variant="outline" size="default">
            <Download size={16} className="mr-2"/>
            Exportar CSV
          </Button>
        </div>
      </div>

      {/* Sentiment Filters */}
      <div className="flex gap-3 flex-wrap">
        <FilterChip 
          active={sentFilter.includes("positivo")}
          onClick={()=>toggleSent("positivo")}
          color="success"
        >
          <Circle size={8} className="mr-1.5"/>
          Positivo
        </FilterChip>
        <FilterChip 
          active={sentFilter.includes("neutro")}
          onClick={()=>toggleSent("neutro")}
          color="warning"
        >
          <Circle size={8} className="mr-1.5"/>
          Neutro
        </FilterChip>
        <FilterChip 
          active={sentFilter.includes("negativo")}
          onClick={()=>toggleSent("negativo")}
          color="danger"
        >
          <Circle size={8} className="mr-1.5"/>
          Negativo
        </FilterChip>
      </div>

      {/* Table */}
      <Card className="shadow-soft">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead className="text-left text-muted-foreground border-b border-border">
              <tr>
                <th className="py-3 px-4 font-medium">Protocolo</th>
                <th className="py-3 px-4 font-medium">Cliente</th>
                <th className="py-3 px-4 font-medium">Agente</th>
                <th className="py-3 px-4 font-medium">Sentimento</th>
                <th className="py-3 px-4 font-medium">Resumo</th>
                <th className="py-3 px-4 font-medium">Duração</th>
                <th className="py-3 px-4 font-medium">Qualidade</th>
                <th className="py-3 px-4 font-medium">Iniciado</th>
                <th className="py-3 px-4 font-medium">Ações</th>
              </tr>
            </thead>
            <tbody>
              {data.items.map(t => (
                <tr key={t.id} className="border-b border-border hover:bg-muted/50 transition-colors">
                  <td className="py-3 px-4 font-mono text-foreground">{t.protocolo}</td>
                  <td className="py-3 px-4">
                    <div className="text-foreground">{t.cliente}</div>
                    <div className="text-xs text-muted-foreground">{t.telefone}</div>
                  </td>
                  <td className="py-3 px-4 text-foreground">{t.agente}</td>
                  <td className="py-3 px-4">
                    <SentTag s={t.sentimento} />
                  </td>
                  <td className="py-3 px-4 max-w-[340px] truncate text-foreground" title={t.resumo}>
                    {t.resumo}
                  </td>
                  <td className="py-3 px-4 text-foreground">{formatDur(t.duracaoSeg)}</td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <div className="w-20 h-2 rounded-full bg-muted">
                        <div 
                          className="h-2 rounded-full transition-all" 
                          style={{ 
                            width: `${t.scoreQualidade}%`, 
                            backgroundColor: t.scoreQualidade>=70 ? "hsl(var(--success))" : t.scoreQualidade>=50 ? "hsl(var(--warning))" : "hsl(var(--danger))"
                          }} 
                        />
                      </div>
                      <span className="text-foreground tabular-nums text-xs">{t.scoreQualidade}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2 text-muted-foreground text-xs">
                      <Clock size={12}/>
                      {new Date(t.criadoEm).toLocaleString()}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <Button size="sm" onClick={()=>onOpenDetalhes(t.id)}>
                      Abrir
                      <ChevronRight size={12} className="ml-1"/>
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-muted-foreground text-sm">Total: {data.total} registros</div>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={()=>setPage(p=>Math.max(1,p-1))}
          >
            <ChevronLeft size={16}/>
          </Button>
          <Badge variant="outline" className="px-3 py-1">
            Página {page}
          </Badge>
          <Button 
            variant="outline" 
            size="sm"
            onClick={()=>{ 
              const max = Math.ceil(data.total/pageSize)||1; 
              setPage(p=>Math.min(max,p+1)); 
            }}
          >
            <ChevronRight size={16}/>
          </Button>
        </div>
      </div>
    </div>
  );
}

function FilterChip({ active, onClick, children, color = "neutral" }: { 
  active: boolean; 
  onClick: () => void; 
  children: React.ReactNode;
  color?: "success" | "warning" | "danger" | "neutral";
}) {
  const colorClasses = {
    success: "border-success/50 text-success",
    warning: "border-warning/50 text-warning",
    danger: "border-danger/50 text-danger",
    neutral: "border-neutral/50 text-neutral",
  };

  return (
    <Button
      variant="ghost"
      size="pill"
      onClick={onClick}
      className={`border ${active ? colorClasses[color] : "border-border text-muted-foreground"} hover:bg-muted`}
    >
      {children}
    </Button>
  );
}

function SentTag({ s }:{ s: Transcript["sentimento"] }){
  const variants = {
    positivo: { label: "Positivo", variant: "success" as const },
    neutro:   { label: "Neutro", variant: "warning" as const },
    negativo: { label: "Negativo", variant: "danger" as const },
  };
  
  const config = variants[s];
  
  return (
    <Badge variant={config.variant} className="text-xs">
      {config.label}
    </Badge>
  );
}

// --- Detalhes --------------------------------------------------------------
function Detalhes({ id, onBack }:{ id:string; onBack:()=>void }){
  const [t, setT] = useState<Transcript | undefined>();
  React.useEffect(()=>{ Api.obterTranscricao(id).then(setT); },[id]);

  if (!t) return (
    <div className="flex items-center gap-2 text-muted-foreground">
      <Info size={16}/>
      Carregando detalhes...
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 flex-wrap">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft size={16} className="mr-2"/>
          Voltar
        </Button>
        <h2 className="text-2xl font-semibold text-foreground">Detalhes do Atendimento</h2>
        <Badge variant="outline" className="flex items-center gap-1">
          <PhoneCall size={12}/>
          Protocolo {t.protocolo}
        </Badge>
        <Badge variant="outline" className="flex items-center gap-1">
          <Tag size={12}/> 
          {t.tags.slice(0,3).join(" · ")}{t.tags.length>3?"…":""}
        </Badge>
      </div>

      <div className="grid xl:grid-cols-4 gap-4 md:gap-6">
        {/* Transcript */}
        <Card className="xl:col-span-3 p-4 md:p-6 shadow-soft">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
            <h3 className="font-semibold text-foreground text-sm md:text-base">Transcrição</h3>
            <span className="text-xs text-muted-foreground">
              Apenas texto – áudio bruto não é armazenado
            </span>
          </div>
          <div className="space-y-3 md:space-y-4 max-h-[50vh] md:max-h-[60vh] overflow-auto pr-1 md:pr-2">
            {t.transcricao.map((turno, i)=> (
              <div key={i} className="flex gap-2 md:gap-3">
                <div className="w-12 md:w-16 text-right text-muted-foreground text-xs pt-1 md:pt-2 flex-shrink-0">
                  {i%2===0?"Agente":"Cliente"}
                </div>
                <Card className="flex-1 p-3 md:p-4 bg-muted/30">
                  <p className="text-foreground text-xs md:text-sm leading-relaxed">{turno}</p>
                </Card>
              </div>
            ))}
          </div>
        </Card>

        {/* Metadata Sidebar */}
        <Card className="p-4 md:p-6 shadow-soft">
          <h3 className="font-semibold text-foreground mb-4 text-sm md:text-base">Metadados</h3>
          <div className="space-y-3">
            <MetaRow k="Cliente" v={`${t.cliente} (${t.telefone})`} />
            <MetaRow k="Agente" v={t.agente} />
            <MetaRow k="Início" v={new Date(t.criadoEm).toLocaleString()} />
            <MetaRow k="Duração" v={formatDur(t.duracaoSeg)} />
            <MetaRow k="Canal" v={t.canal} />
            <MetaRow k="Sentimento" v={<SentTag s={t.sentimento} />} />
            <MetaRow k="Qualidade" v={<span className="tabular-nums">{t.scoreQualidade}</span>} />
          </div>
        </Card>
      </div>
    </div>
  );
}

function MetaRow({ k, v }:{ k:string; v:any }){
  return (
    <div className="flex items-center justify-between py-2 border-b border-border last:border-0">
      <span className="text-muted-foreground text-xs uppercase tracking-wide font-medium">{k}</span>
      <div className="text-sm text-foreground">{v}</div>
    </div>
  );
}

// --- Utils -----------------------------------------------------------------
function formatDur(total:number){
  const m = Math.floor(total/60).toString();
  const s = (total%60).toString().padStart(2, "0");
  return `${m}m ${s}s`;
}

// --- Mock DB ---------------------------------------------------------------
const MOCK_DATA: Transcript[] = [
  {
    id: "t1",
    protocolo: "2025-0910-7731",
    cliente: "Gabriela Rocha",
    telefone: "(11) 91234-5678",
    criadoEm: new Date().toISOString(),
    duracaoSeg: 412,
    sentimento: "neutro",
    resumo: "Baixa pressão pontual na região; agendada visita técnica.",
    tags: ["baixa-pressao", "visita-tecnica", "seguranca"],
    scoreQualidade: 78,
    canal: "voz",
    agente: "Marcos",
    transcricao: [
      "Boa tarde! Aqui é o Marcos, do atendimento Comgás. Como posso ajudar?",
      "Minha casa está com o fogão falhando, a chama está fraca desde ontem.",
      "Entendo. Poderia me informar quando ocorreu e se houve alguma manutenção recente?",
      "Começou ontem à noite. Não fiz nenhuma manutenção.",
      "Obrigado. Vou consultar as leituras da região e a pressão da rede. Um instante.",
      "Tudo bem.",
      "Identificamos baixa pressão pontual. Vou abrir um chamado para visita técnica. Protocolo 2025-0910-7731.",
      "Certo, obrigada.",
    ],
  },
  {
    id: "t2",
    protocolo: "2025-0911-1130",
    cliente: "Diego Silva",
    telefone: "(11) 99876-5432",
    criadoEm: new Date(Date.now()-3600_000).toISOString(),
    duracaoSeg: 305,
    sentimento: "positivo",
    resumo: "Troca de válvula concluída; orientações de segurança repassadas.",
    tags: ["manutencao", "seguranca"],
    scoreQualidade: 86,
    canal: "voz",
    agente: "Carolina",
    transcricao: [
      "Olá, aqui é a Carolina da Comgás. Como posso ajudar?",
      "A válvula do gás parece estar com vazamento.",
      "Vamos seguir um procedimento de segurança. Pode fechar o registro principal agora?",
      "Sim, feito.",
      "Perfeito. Vou encaminhar a equipe e registrar a ocorrência."
    ],
  },
  {
    id: "t3",
    protocolo: "2025-0912-8819",
    cliente: "Ana Souza",
    telefone: "(11) 93322-1100",
    criadoEm: new Date(Date.now()-7200_000).toISOString(),
    duracaoSeg: 198,
    sentimento: "negativo",
    resumo: "Cliente insatisfeita com tempo de espera; fornecidas alternativas e novo agendamento.",
    tags: ["tempo-espera", "agendamento"],
    scoreQualidade: 61,
    canal: "voz",
    agente: "Rodrigo",
    transcricao: [
      "Central Comgás, Rodrigo falando.",
      "Estou esperando retorno há dois dias!",
      "Sinto muito pela espera. Vou priorizar seu chamado agora mesmo.",
      "Obrigada. Espero que resolvam hoje.",
    ],
  },
  {
    id: "t4",
    protocolo: "2025-0913-4567",
    cliente: "Carlos Mendes",
    telefone: "(11) 95555-4321",
    criadoEm: new Date(Date.now()-10800_000).toISOString(),
    duracaoSeg: 180,
    sentimento: "positivo",
    resumo: "Instalação de novo medidor agendada com sucesso.",
    tags: ["instalacao", "medidor"],
    scoreQualidade: 92,
    canal: "voz",
    agente: "Patricia",
    transcricao: [
      "Comgás, Patricia falando. Como posso ajudar?",
      "Gostaria de instalar um medidor novo no meu apartamento.",
      "Claro! Vou verificar a disponibilidade para sua região.",
      "Obrigado, aguardo."
    ],
  },
  {
    id: "t5",
    protocolo: "2025-0913-7890",
    cliente: "Fernanda Costa",
    telefone: "(11) 91111-2222",
    criadoEm: new Date(Date.now()-14400_000).toISOString(),
    duracaoSeg: 256,
    sentimento: "neutro",
    resumo: "Dúvidas sobre fatura esclarecidas.",
    tags: ["fatura", "esclarecimento"],
    scoreQualidade: 75,
    canal: "voz",
    agente: "Roberto",
    transcricao: [
      "Olá, aqui é o Roberto da Comgás.",
      "Tenho uma dúvida sobre minha conta.",
      "Claro, pode me explicar qual é a dúvida?",
      "O valor veio mais alto que o normal."
    ],
  },
  {
    id: "t6",
    protocolo: "2025-0914-1234",
    cliente: "João Pereira",
    telefone: "(11) 92222-3333",
    criadoEm: new Date(Date.now()-18000_000).toISOString(),
    duracaoSeg: 145,
    sentimento: "negativo",
    resumo: "Reclamação sobre cobrança indevida registrada.",
    tags: ["cobranca", "reclamacao"],
    scoreQualidade: 58,
    canal: "voz",
    agente: "Lucia",
    transcricao: [
      "Central Comgás, Lucia ao telefone.",
      "Estou sendo cobrado por algo que não usei!",
      "Vou verificar seu histórico imediatamente.",
      "Por favor, resolva isso hoje."
    ],
  },
  {
    id: "t7",
    protocolo: "2025-0914-5678",
    cliente: "Maria Santos",
    telefone: "(11) 93333-4444",
    criadoEm: new Date(Date.now()-21600_000).toISOString(),
    duracaoSeg: 320,
    sentimento: "positivo",
    resumo: "Mudança de titularidade processada com sucesso.",
    tags: ["titularidade", "mudanca"],
    scoreQualidade: 88,
    canal: "voz",
    agente: "Eduardo",
    transcricao: [
      "Comgás, Eduardo falando. Em que posso ajudar?",
      "Preciso mudar a titularidade da conta.",
      "Sem problemas. Vou precisar de alguns documentos.",
      "Tenho tudo aqui comigo."
    ],
  },
  {
    id: "t8",
    protocolo: "2025-0915-9012",
    cliente: "Ricardo Alves",
    telefone: "(11) 94444-5555",
    criadoEm: new Date(Date.now()-25200_000).toISOString(),
    duracaoSeg: 89,
    sentimento: "neutro",
    resumo: "Informações sobre novos planos fornecidas.",
    tags: ["planos", "informacao"],
    scoreQualidade: 82,
    canal: "voz",
    agente: "Camila",
    transcricao: [
      "Olá, Camila da Comgás.",
      "Quais são os planos disponíveis?",
      "Temos três opções principais...",
      "Obrigado pela informação."
    ],
  },
  {
    id: "t9",
    protocolo: "2025-0915-3456",
    cliente: "Sandra Lima",
    telefone: "(11) 95555-6666",
    criadoEm: new Date(Date.now()-28800_000).toISOString(),
    duracaoSeg: 275,
    sentimento: "positivo",
    resumo: "Agendamento de manutenção preventiva realizado.",
    tags: ["manutencao", "preventiva"],
    scoreQualidade: 90,
    canal: "voz",
    agente: "Bruno",
    transcricao: [
      "Central Comgás, Bruno ao telefone.",
      "Gostaria de agendar uma manutenção preventiva.",
      "Claro! Vamos verificar as datas disponíveis.",
      "Perfeito, muito obrigada."
    ],
  },
  {
    id: "t10",
    protocolo: "2025-0916-7890",
    cliente: "Paulo Oliveira",
    telefone: "(11) 96666-7777",
    criadoEm: new Date(Date.now()-32400_000).toISOString(),
    duracaoSeg: 67,
    sentimento: "negativo",
    resumo: "Problema de vazamento reportado como urgente.",
    tags: ["vazamento", "urgente"],
    scoreQualidade: 45,
    canal: "voz",
    agente: "Tatiana",
    transcricao: [
      "Comgás, Tatiana falando.",
      "Há um vazamento na rua!",
      "Vou encaminhar urgentemente!",
      "Por favor, sejam rápidos!"
    ],
  }
];