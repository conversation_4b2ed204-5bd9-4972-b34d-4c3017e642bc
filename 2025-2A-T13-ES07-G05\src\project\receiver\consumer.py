import os, json, sys
from dotenv import load_dotenv
from pika.exceptions import AMQPConnectionError
from common.rabbitmq import get_connection, get_channel

load_dotenv()
QUEUE = os.getenv("RABBITMQ_QUEUE", "atendimento.religacao")

def consumer_callback(ch, method, properties, body):
    try:
        data = json.loads(body)  
        print(f"[x] Received: {data}")
        ch.basic_ack(method.delivery_tag)  
    except Exception as e:
        print(f"[!] Error processing message: {e}")
        
        ch.basic_nack(method.delivery_tag, requeue=False)

def main():
    try:
        connection = get_connection()
        channel = get_channel(connection)

        channel.queue_declare(queue=QUEUE, durable=True)
        channel.basic_qos(prefetch_count=1)

        channel.basic_consume(
            queue=QUEUE,
            on_message_callback=consumer_callback,
            auto_ack=False
        )

        print(f"[*] Waiting for messages on '{QUEUE}'. CTRL+C to exit")
        channel.start_consuming()

    except AMQPConnectionError as e:
        print(f"Connection error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

    
