import os, sys, json, base64, tempfile, statistics
from pathlib import Path
from dotenv import load_dotenv
from pika.exceptions import AMQPConnectionError
from datetime import datetime, timezone

# Garante import do pacote 'common'
PROJECT_ROOT = Path(__file__).resolve().parents[1]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

from common.rabbitmq import get_connection, get_channel  # noqa: E402

load_dotenv()
QUEUE = os.getenv("RABBITMQ_QUEUE", "transcription.audio")
WHISPER_MODEL = "turbo"

def _decode_to_tempfile(payload: dict) -> str:
    b64 = payload.get("audioContent")
    if not b64:
        raise ValueError("Mensagem sem 'audioContent'")
    raw = base64.b64decode(b64)
    ct = (payload.get("contentType") or "").lower()
    suffix = ".wav" if "wav" in ct else ".mp3" if ("mp3" in ct or "mpeg" in ct) else ".bin"
    f = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
    f.write(raw)
    f.flush(); f.close()
    return f.name

def _transcribe_whisper(audio_path: str) -> dict:
    """
    Retorna o resultado necessário do whisper:
    {
      'text': '...',
      'segments': [...],
      'language': 'pt'
    }
    """
    import whisper
    print(f"[consumer] Carregando modelo Whisper='{WHISPER_MODEL}'...")
    model = whisper.load_model(WHISPER_MODEL)
    result = model.transcribe(audio_path, language="pt")
    return {
        "text": (result or {}).get("text", "").strip(),
        "segments": (result or {}).get("segments", []) or [],
        "language": (result or {}).get("language", "pt")
    }

def _atomic_write_json(path: Path, data: dict):
    tmp_path = path.with_suffix(path.suffix + ".tmp")
    with tmp_path.open("w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    tmp_path.replace(path)

def consumer_callback(ch, method, properties, body):
    try:
        payload = json.loads(body)
        print(f"[consumer] Mensagem recebida: id={payload.get('messageId')} file={payload.get('filename')} size={payload.get('sizeBytes')}B")

        audio_path = _decode_to_tempfile(payload)
        print(f"[consumer] Áudio salvo em: {audio_path}")

        w = _transcribe_whisper(audio_path)
        text = w.get("text", "")
        segments = w.get("segments", []) or []

        # confianca_media = média de avg_logprob (se disponível nos segments)
        avg_logs = [s.get("avg_logprob") for s in segments if isinstance(s.get("avg_logprob"), (int, float))]
        confianca_media = (statistics.fmean(avg_logs) if avg_logs else None)

        # Quantidade de segmentos
        total_segmentos = len(segments)

        # Diretório e nome do arquivo de saída
        out_dir = Path(__file__).resolve().parents[2] / "backend" / "data" / "transcricoes"
        out_dir.mkdir(parents=True, exist_ok=True)
        message_id = payload.get("messageId", "sem-id")
        json_path = out_dir / f"{message_id}.json"

        # Duração: aceita durationSeconds ou duration_seconds do payload
        duracao = payload.get("durationSeconds")
        if duracao is None:
            duracao = payload.get("duration_seconds")

        # Monta o JSON minimalista solicitado
        doc = {
            "arquivo": payload.get("filename"),
            "modelo": os.getenv("WHISPER_MODEL_ALIAS", WHISPER_MODEL),  # "turbo" se quiser mapear via env
            "tipo": payload.get("contentType"),
            "duracao_segundos": float(duracao) if duracao is not None else None,
            "confianca_media": float(confianca_media) if isinstance(confianca_media, (int, float)) else None,
            "segmentos": int(total_segmentos),
            "texto": text
        }

        # Salva como JSON
        _atomic_write_json(json_path, doc)
        print(f"[consumer] JSON salvo em: {json_path}")

        ch.basic_ack(delivery_tag=method.delivery_tag)
    except Exception as e:
        print("[consumer] Erro ao processar mensagem:", repr(e))
        ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

def main():
    connection = None
    try:
        connection = get_connection()
        channel = get_channel(connection)
        channel.queue_declare(queue=QUEUE, durable=True)
        channel.basic_qos(prefetch_count=1)
        channel.basic_consume(queue=QUEUE, on_message_callback=consumer_callback)
        print(f"[*] Waiting for messages on '{QUEUE}'. CTRL+C to exit")
        channel.start_consuming()
    except KeyboardInterrupt:
        print("\n[consumer] Interrompido pelo usuário.")
    except AMQPConnectionError as e:
        print("[consumer] Erro de conexão RabbitMQ:", str(e))
    finally:
        try:
            if connection and connection.is_open:
                connection.close()
        except Exception:
            pass

if __name__ == "__main__":
    main()